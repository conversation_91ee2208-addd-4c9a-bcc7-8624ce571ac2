providers:
  default: elevenlabs
  elevenlabs:
    voice_id: "At0PgxDCGLVlDr0N86Ty"
    # Add other provider-specific configurations here

audio_processing:
  # Audio filtering settings
  min_chunk_size: 50          # Minimum audio chunk size to process (bytes)
  substantial_chunk_size: 100  # Size considered substantial for new recording detection

  # Quality settings
  enable_filtering: true       # Enable basic audio filtering
  filter_silence: true         # Filter out silent/very quiet chunks

  # ElevenLabs specific settings
  voice_settings:
    stability: 0.5
    similarity_boost: 0.8
    style: 0.5
    use_speaker_boost: true

  generation_config:
    chunk_length_schedule: [50, 120, 200]
    use_cpu: false
    temperature: 0.7