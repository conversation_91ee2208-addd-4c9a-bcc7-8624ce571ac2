#!/usr/bin/env python3
"""
Simple LiveKit Agent for Testing
Basic voice AI companion using LiveKit Agents framework
"""

import asyncio
import logging
import os

from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
    stt,
    tts,
    vad,
)
from livekit.plugins import deepgram, elevenlabs, silero
from livekit import rtc

# Environment setup
from dotenv import load_dotenv
load_dotenv()

logger = logging.getLogger("voice-assistant")
logger.setLevel(logging.INFO)

class SimpleLLM(llm.LLM):
    """Simple LLM that responds with basic messages"""

    def __init__(self):
        super().__init__()
        self.responses = [
            "Hello! I'm your AI voice assistant. How can I help you today?",
            "That's interesting! Tell me more about that.",
            "I understand. Is there anything specific you'd like to know?",
            "Great question! Let me think about that for a moment.",
            "I'm here to help with whatever you need. What would you like to discuss?",
        ]
        self.response_index = 0

    async def agenerate(
        self,
        *,
        message: str,
        **kwargs,
    ) -> llm.LLMStream:
        """Generate a simple response"""

        # Cycle through responses
        response_text = self.responses[self.response_index % len(self.responses)]
        self.response_index += 1

        # Create a simple stream that yields the response
        class SimpleStream(llm.LLMStream):
            def __init__(self, text: str):
                super().__init__()
                self._text = text
                self._sent = False

            async def __anext__(self) -> llm.ChatChunk:
                if self._sent:
                    raise StopAsyncIteration
                self._sent = True
                return llm.ChatChunk(
                    choices=[
                        llm.Choice(
                            delta=llm.ChoiceDelta(content=self._text, role="assistant"),
                            index=0
                        )
                    ]
                )

        return SimpleStream(response_text)

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent"""

    logger.info("Starting simple voice assistant...")

    # Wait for the first participant to connect
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    logger.info("Participant connected, initializing AI components...")

    # Initialize AI components
    vad_instance = silero.VAD.load()
    stt_instance = deepgram.STT()
    llm_instance = SimpleLLM()
    tts_instance = elevenlabs.TTS(voice="At0PgxDCGLVlDr0N86Ty")

    logger.info("AI components initialized successfully!")

    # Simple test - just say hello
    logger.info("Generating welcome message...")

    # Generate welcome audio
    welcome_text = "Hello! I'm your AI voice assistant. How can I help you today?"
    audio_stream = tts_instance.synthesize(welcome_text)

    # Play the welcome message
    logger.info("Playing welcome message...")
    audio_source = rtc.AudioSource(sample_rate=24000, num_channels=1)
    track = rtc.LocalAudioTrack.create_audio_track("assistant-voice", audio_source)

    # Publish the audio track
    await ctx.room.local_participant.publish_track(track, rtc.TrackPublishOptions())

    # Stream the audio
    async for audio_frame in audio_stream:
        await audio_source.capture_frame(audio_frame)

    logger.info("Voice assistant started successfully!")

    # Keep the agent running
    while True:
        await asyncio.sleep(1)

if __name__ == "__main__":
    # Configure worker options
    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
    )

    # Start the agent
    cli.run_app(worker_options)
