#!/usr/bin/env python3
"""
Test LiveKit room creation and connection
"""

import os
import asyncio
from livekit import api
from dotenv import load_dotenv

async def test_livekit_connection():
    """Test LiveKit connection and room creation"""
    
    load_dotenv()
    
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    livekit_url = os.getenv("LIVEKIT_URL")
    
    if not all([api_key, api_secret, livekit_url]):
        print("❌ Missing LiveKit credentials")
        return
    
    print("🔧 Testing LiveKit connection...")
    print(f"📡 URL: {livekit_url}")
    print(f"🔑 API Key: {api_key}")
    
    try:
        # Create room service
        room_service = api.RoomService(livekit_url, api_key, api_secret)
        
        # List existing rooms
        print("\n📋 Listing existing rooms...")
        rooms = await room_service.list_rooms()
        print(f"Found {len(rooms)} rooms:")
        for room in rooms:
            print(f"  - {room.name} (SID: {room.sid})")
        
        # Create a test room
        room_name = "test-room"
        print(f"\n🏗️ Creating room: {room_name}")
        
        try:
            room = await room_service.create_room(
                api.CreateRoomRequest(name=room_name)
            )
            print(f"✅ Room created successfully!")
            print(f"   Name: {room.name}")
            print(f"   SID: {room.sid}")
        except Exception as e:
            if "already exists" in str(e):
                print(f"✅ Room already exists: {room_name}")
            else:
                print(f"❌ Error creating room: {e}")
        
        # Generate access token
        print(f"\n🎫 Generating access token...")
        token = api.AccessToken(api_key, api_secret) \
            .with_identity("test-user") \
            .with_name("Test User") \
            .with_grants(api.VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=True,
                can_subscribe=True,
                can_publish_data=True,
            ))
        
        jwt_token = token.to_jwt()
        print(f"✅ Token generated successfully!")
        print(f"Token: {jwt_token[:50]}...")
        
        print(f"\n🎯 LiveKit setup is working correctly!")
        print(f"📱 You can now connect to room '{room_name}' using the generated token")
        
        return jwt_token
        
    except Exception as e:
        print(f"❌ LiveKit connection test failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(test_livekit_connection())
