from fastapi import FastAPI, UploadFile, File, HTTPException, WebSocket
from fastapi.responses import StreamingResponse
import elevenlabs
import os
import yaml
import logging
from dotenv import load_dotenv
from abc import ABC, abstractmethod
from langchain_google_genai import <PERSON>Generative<PERSON>IEmbeddings, ChatGoogleGenerativeAI
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import CharacterTextSplitter
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory
from typing import Dict, Any, Union
import asyncio
import websockets # Import websockets library for connecting to ElevenLabs WS
import json
from fastapi.websockets import WebSocketState

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Load configuration
try:
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)
    logger.info("Configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load configuration: {str(e)}")
    raise

# Retrieve API key explicitly
google_api_key = os.getenv("GOOGLE_API_KEY")
if not google_api_key:
    logger.error("GOOGLE_API_KEY not found in environment variables")
    # Consider raising an exception or handling this more gracefully in a real app

# Initialize LangChain components with Gemini, passing API key explicitly
embeddings = GoogleGenerativeAIEmbeddings(model="models/text-embedding-004", google_api_key=google_api_key)
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
# memory = ConversationBufferMemory() # Initialize per WebSocket connection
# conversation = ConversationChain(memory=memory) # Initialize per WebSocket connection

# Initialize Google Generative AI LLM
llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash-exp", google_api_key=google_api_key, temperature=0.7)

# Base S2S Interface (Still useful for potential other providers)
class SpeechToSpeechProvider(ABC):
    @abstractmethod
    async def generate_speech(self, audio_input: bytes) -> bytes:
        pass

# ElevenLabs S2S Provider (Placeholder/Example - main logic will be in WebSocket)
class ElevenLabsProvider(SpeechToSpeechProvider):
    def __init__(self, config: Dict[str, Any]):
        self.api_key = os.getenv("ELEVENLABS_API_KEY")
        if not self.api_key:
            logger.error("ELEVENLABS_API_KEY not found in environment variables")
            raise ValueError("ELEVENLABS_API_KEY not found in environment variables")
        elevenlabs.set_api_key(self.api_key)
        self.voice_id = config.get("voice_id", "default")
        logger.info(f"Initialized ElevenLabs provider with voice_id: {self.voice_id}")

    async def generate_speech(self, audio_input: bytes) -> bytes:
        # This method might be simplified or removed if using full WebSocket for S2S
        logger.info("Generating speech from audio input using ElevenLabs (via HTTP call)")
        # Placeholder for ASR and interaction with LLM/LangChain
        input_text = "This is a placeholder for transcribed text."
        # response_text = conversation.predict(input=input_text) # Use conversation instance per connection
        response_text = "Placeholder response from HTTP endpoint"

        try:
            # Using the standard ElevenLabs generate for simplicity in this placeholder
            audio = elevenlabs.generate(
                text=response_text,
                voice=self.voice_id,
                model="eleven_monolingual_v1"
            )
            logger.info("Successfully generated speech via HTTP call")
            return audio
        except Exception as e:
            logger.error(f"Speech generation (HTTP) failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Speech generation failed: {str(e)}")

# Provider Factory
class S2SProviderFactory:
    @staticmethod
    def get_provider(name: str, provider_config: Dict[str, Any]) -> SpeechToSpeechProvider:
        try:
            logger.info(f"Initializing S2S provider: {name}")
            if name == "elevenlabs":
                return ElevenLabsProvider(provider_config)
            raise ValueError(f"Unknown provider: {name}")
        except Exception as e:
            logger.error(f"Provider initialization failed: {str(e)}")
            raise

# Initialize FastAPI
app = FastAPI()

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint to verify backend status and configuration"""
    google_api_key = os.getenv("GOOGLE_API_KEY")
    elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")

    status = {
        "status": "healthy",
        "google_api_configured": bool(google_api_key),
        "elevenlabs_api_configured": bool(elevenlabs_api_key),
        "voice_id_configured": bool(config.get("providers", {}).get("elevenlabs", {}).get("voice_id")),
        "voice_id": config.get("providers", {}).get("elevenlabs", {}).get("voice_id", "not_configured")
    }

    if not google_api_key or not elevenlabs_api_key:
        status["status"] = "configuration_error"
        status["message"] = "Missing required API keys"

    return status

# Initialize S2S Provider (still use factory for potential fallback or other providers)
try:
    logger.info("Initializing S2S provider from configuration")
    # Note: This might be used for non-realtime processing or if a provider doesn't support WebSockets
    # s2s_provider = S2SProviderFactory.get_provider(
    #     config["providers"]["default"],
    #     config["providers"][config["providers"]["default"]]
    # )
    s2s_provider = None # For now, explicitly set to None as we prioritize WebSocket
except Exception as e:
    logger.critical(f"Failed to initialize S2S provider: {str(e)}")
    # Don't raise critical error if this is just for fallback
    s2s_provider = None # Set to None if initialization fails


# --- WebSocket Endpoint for Real-time S2S --- #

ELEVENLABS_WS_URL = f"wss://api.elevenlabs.io/v1/text-to-speech/{config['providers']['elevenlabs']['voice_id']}/stream-input?model_id=eleven_monolingual_v1" # Using voice_id from config

# Expected audio format from client for ElevenLabs WebSocket stream-input:
# - Codec: PCM (linear16) or Opus (preferred for lower latency)
# - Sample Rate: 16000 Hz, 22050 Hz, or 44100 Hz
# - Channels: 1 (mono)
# - Bit Depth: 16-bit (for PCM)
# Note: The frontend currently sends AAC LC at 16000 Hz. ElevenLabs supports this format.
# If there are issues, we might need to convert to PCM or Opus.
@app.websocket("/ws/audio")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    client_host, client_port = websocket.scope["client"]
    session_id = f"{client_host}:{client_port}"
    logger.info(f"WebSocket connection accepted from {session_id}")
    logger.info("Using persistent WebSocket connection for two-way communication")

    store: Dict[str, ChatMessageHistory] = {}
    def get_session_history(session_id: str) -> ChatMessageHistory:
        if session_id not in store:
            store[session_id] = ChatMessageHistory()
        return store[session_id]

    client_host, client_port = websocket.scope["client"]
    session_id = f"{client_host}:{client_port}"

    prompt = ChatPromptTemplate.from_messages([
        MessagesPlaceholder(variable_name="history"),
        ("human", "{input}"),
    ])
    runnable = llm
    conversation_chain = RunnableWithMessageHistory(
        runnable,
        get_session_history,
        input_messages_key="input",
        history_messages_key="history",
        prompt=prompt
    )

    elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
    if not elevenlabs_api_key:
         logger.error("ELEVENLABS_API_KEY not found for WebSocket connection")
         await websocket.close(code=1011, reason="ELEVENLABS_API_KEY not configured on backend")
         return

    # Validate that we have the required configuration
    if not config.get("providers", {}).get("elevenlabs", {}).get("voice_id"):
        logger.error("ElevenLabs voice_id not found in configuration")
        await websocket.close(code=1011, reason="ElevenLabs voice_id not configured")
        return

    client_disconnected_event = asyncio.Event()

    try:
        logger.info(f"Connecting to ElevenLabs WebSocket: {ELEVENLABS_WS_URL}")
        logger.info(f"Using voice_id: {config['providers']['elevenlabs']['voice_id']}")

        # Create a persistent connection to ElevenLabs
        try:
            elevenlabs_ws = await websockets.connect(
                ELEVENLABS_WS_URL,
                additional_headers=[('xi-api-key', elevenlabs_api_key)],
                ping_interval=20,  # Send ping every 20 seconds
                ping_timeout=10,    # Wait 10 seconds for pong
                close_timeout=10,   # Wait 10 seconds when closing
                max_queue=1024 * 1024  # 1MB max queue size
            )
            logger.info("Successfully connected to ElevenLabs WebSocket")
        except Exception as e:
            logger.error(f"Failed to connect to ElevenLabs WebSocket: {str(e)}")
            await websocket.close(code=1011, reason=f"Failed to connect to ElevenLabs: {str(e)}")
            return

        # Initialize the ElevenLabs WebSocket with configuration
        voice_settings = config.get("audio_processing", {}).get("voice_settings", {
            "stability": 0.5,
            "similarity_boost": 0.8,
            "style": 0.5,
            "use_speaker_boost": True
        })

        generation_config = config.get("audio_processing", {}).get("generation_config", {
            "chunk_length_schedule": [50, 120, 200],
            "use_cpu": False,
            "temperature": 0.7
        })

        init_message = {
            "text": " ",  # Single space to initialize
            "voice_settings": voice_settings,
            "generation_config": generation_config,
            "model_id": "eleven_monolingual_v2"
        }
        try:
            await elevenlabs_ws.send(json.dumps(init_message))
            logger.info("Sent initialization message to ElevenLabs WebSocket")
        except Exception as e:
            logger.error(f"Failed to send initialization message to ElevenLabs: {str(e)}")
            await websocket.close(code=1011, reason="Failed to initialize ElevenLabs connection")
            return

        async def send_end_of_stream(ws_conn, end_reason=""):
            """Helper to send the end-of-stream message to ElevenLabs"""
            if not ws_conn:
                logger.warning(f"[Backend->11Labs] Cannot send end-of-stream - connection is None ({end_reason})")
                return False

            # Check if connection is closed using hasattr to avoid attribute errors
            is_closed = True
            try:
                # For websockets.WebSocketClientProtocol, check the state properly
                if hasattr(ws_conn, 'closed'):
                    is_closed = ws_conn.closed
                elif hasattr(ws_conn, 'state'):
                    # Check if state indicates closed connection
                    is_closed = str(ws_conn.state) in ['CLOSED', 'CLOSING']
                else:
                    is_closed = True
            except Exception:
                # If we can't access the closed attribute, assume it's closed
                is_closed = True

            if is_closed:
                logger.warning(f"[Backend->11Labs] Cannot send end-of-stream - connection already closed ({end_reason})")
                return False

            try:
                logger.info(f"[Backend->11Labs] Sending end-of-stream message ({end_reason})")
                await ws_conn.send(json.dumps({"text": ""}))
                logger.info(f"[Backend->11Labs] Successfully sent end-of-stream message ({end_reason})")
                return True
            except websockets.exceptions.ConnectionClosed:
                logger.warning(f"[Backend->11Labs] Connection closed when sending end-of-stream ({end_reason})")
                return False
            except Exception as e:
                logger.error(f"[Backend->11Labs] Error sending end-of-stream ({end_reason}): {str(e)}")
                return False

        async def handle_client_audio(client_ws: WebSocket, elevenlabs_ws_conn: websockets.WebSocketClientProtocol, event: asyncio.Event):
            end_of_stream_sent = False
            audio_chunks_received = 0
            last_audio_time = asyncio.get_event_loop().time()

            # Helper function to safely check if a WebSocket is closed
            def is_connection_closed(ws):
                if not ws:
                    return True
                try:
                    # For websockets.WebSocketClientProtocol, check the state properly
                    if hasattr(ws, 'closed'):
                        return ws.closed
                    elif hasattr(ws, 'state'):
                        # Check if state indicates closed connection
                        return str(ws.state) in ['CLOSED', 'CLOSING']
                    else:
                        return True
                except Exception:
                    return True

            try:
                while not event.is_set():
                    try:
                        message = await asyncio.wait_for(
                            client_ws.receive(),
                            timeout=1.0
                        )
                        logger.debug(f"[Client->Backend] Received message type: {message.get('type', 'unknown')}")
                        if message["type"] == "websocket.receive":
                            if "bytes" in message:
                                audio_data = message["bytes"]
                                if not audio_data:
                                    logger.warning("[Client->Backend] Received empty audio chunk, ignoring")
                                    continue

                                audio_size = len(audio_data)
                                logger.info(f"[Client->Backend] Received audio chunk ({audio_size} bytes)")

                                # Audio quality filtering using config settings
                                min_chunk_size = config.get("audio_processing", {}).get("min_chunk_size", 50)
                                substantial_chunk_size = config.get("audio_processing", {}).get("substantial_chunk_size", 100)
                                enable_filtering = config.get("audio_processing", {}).get("enable_filtering", True)

                                if enable_filtering and audio_size < min_chunk_size:
                                    logger.debug(f"[Client->Backend] Filtering out tiny audio chunk ({audio_size} bytes)")
                                    continue

                                # Update last audio time
                                last_audio_time = asyncio.get_event_loop().time()

                                # Reset end_of_stream_sent flag when receiving substantial audio (new recording started)
                                if audio_size > substantial_chunk_size:  # Substantial audio chunk indicates new recording
                                    audio_chunks_received += 1
                                    if audio_chunks_received == 1:  # First substantial chunk of new recording
                                        logger.info("[Client->Backend] New recording session detected, resetting end-of-stream flag")
                                        end_of_stream_sent = False

                                # Audio chunks of 7 bytes or smaller typically indicate the end of recording
                                if audio_size <= 7 and audio_size > 0:
                                    logger.info("[Client->Backend] Detected small audio chunk, likely end of recording")
                                    # Don't send end-of-stream for small chunks, wait for explicit signal
                                    # if not end_of_stream_sent:
                                    #     end_of_stream_sent = await send_end_of_stream(elevenlabs_ws_conn, "small_chunk")

                                # Still send the audio chunk to ElevenLabs
                                try:
                                    if not is_connection_closed(elevenlabs_ws_conn):
                                        await elevenlabs_ws_conn.send(audio_data)
                                        logger.info("[Backend->11Labs] Sent audio chunk to ElevenLabs")
                                    else:
                                        logger.error("[Backend->11Labs] Cannot send audio - connection already closed")
                                        break
                                except websockets.exceptions.ConnectionClosed:
                                    logger.error("[Backend->11Labs] Connection closed when sending audio")
                                    break

                            elif "text" in message:
                                text_data = message["text"]
                                logger.info(f"[Client->Backend] Control message: {text_data}")
                                try:
                                    msg = json.loads(text_data)
                                    if msg.get('type') == 'end_call':
                                        logger.info("[Client->Backend] Received end_call message")
                                        if not end_of_stream_sent:
                                            end_of_stream_sent = await send_end_of_stream(elevenlabs_ws_conn, "end_call")
                                        return
                                    elif msg.get('type') == 'end_recording':
                                        logger.info("[Client->Backend] Received end_recording message")
                                        logger.info(f"[Client->Backend] end_of_stream_sent flag: {end_of_stream_sent}")
                                        if not end_of_stream_sent:
                                            logger.info("[Client->Backend] Sending end-of-stream to ElevenLabs...")
                                            end_of_stream_sent = await send_end_of_stream(elevenlabs_ws_conn, "end_recording")
                                            logger.info(f"[Client->Backend] End-of-stream sent successfully: {end_of_stream_sent}")
                                        else:
                                            logger.info("[Client->Backend] End-of-stream already sent, skipping")
                                        # Reset counter for next recording session
                                        audio_chunks_received = 0
                                        logger.info("[Client->Backend] Reset audio chunks counter, ready for next recording")
                                        # Don't return, continue listening for more audio
                                    elif msg.get('type') == 'test':
                                        logger.info(f"[Client->Backend] Received test message: {msg.get('message', 'no message')}")
                                        # Send a test response back
                                        try:
                                            await client_ws.send_text(json.dumps({"type": "test_response", "status": "ok"}))
                                        except Exception as e:
                                            logger.error(f"Error sending test response: {str(e)}")
                                except json.JSONDecodeError:
                                    logger.warning(f"[Client->Backend] Invalid JSON: {text_data}")

                        elif message["type"] == "websocket.disconnect":
                            logger.info("[Client->Backend] Client disconnected")
                            if not end_of_stream_sent:
                                end_of_stream_sent = await send_end_of_stream(elevenlabs_ws_conn, "disconnect")
                            break

                    except asyncio.TimeoutError:
                        # No message received, continue
                        continue

            except websockets.exceptions.ConnectionClosed:
                logger.info("[Client->Backend] WebSocket connection closed")
                if not end_of_stream_sent:
                    end_of_stream_sent = await send_end_of_stream(elevenlabs_ws_conn, "connection_closed")

            except Exception as e:
                logger.error(f"[Client->Backend] Error in audio handler: {str(e)}")

            finally:
                # Last attempt to send end-of-stream if not already sent
                if not end_of_stream_sent:
                    await send_end_of_stream(elevenlabs_ws_conn, "finally")

                logger.info("[Client->Backend] Audio handler finished")
                event.set()  # Signal that we're done

        async def handle_elevenlabs_response(client_ws: WebSocket, elevenlabs_ws_conn: websockets.WebSocketClientProtocol, event: asyncio.Event):
            # Helper function to safely check if a WebSocket is closed
            def is_connection_closed(ws):
                if not ws:
                    return True
                try:
                    # For websockets.WebSocketClientProtocol, check the state properly
                    if hasattr(ws, 'closed'):
                        return ws.closed
                    elif hasattr(ws, 'state'):
                        # Check if state indicates closed connection
                        return str(ws.state) in ['CLOSED', 'CLOSING']
                    else:
                        return True
                except Exception:
                    return True

            try:
                while not event.is_set():
                    try:
                        message = await asyncio.wait_for(
                            elevenlabs_ws_conn.recv(),
                            timeout=1.0
                        )
                    except asyncio.TimeoutError:
                        # No message received, check if we should continue
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        logger.info("[11Labs->Backend] ElevenLabs connection closed")
                        break
                    except Exception as e:
                        logger.error(f"[11Labs->Backend] Unexpected error while receiving: {str(e)}")
                        break

                    if message is None:
                        continue

                    logger.info(f"[11Labs->Backend] Received message: {type(message).__name__}")

                    if isinstance(message, str):
                        # Handle text messages (transcriptions, control messages)
                        try:
                            data = json.loads(message)
                            logger.info(f"[11Labs->Backend] Text message: {data}")

                            # Check for error messages from ElevenLabs
                            if data.get('error') == 'connection_lost':
                                logger.error(f"[11Labs->Backend] ElevenLabs connection error: {data.get('message')}")
                                # Don't break - we might still receive more messages
                            elif data.get('error') == 'input_timeout_exceeded':
                                logger.warning(f"[11Labs->Backend] ElevenLabs input timeout: {data.get('message')}")
                                # Send a keep-alive message to maintain connection
                                try:
                                    keep_alive_msg = {"text": " ", "try_trigger_generation": False}
                                    await elevenlabs_ws_conn.send(json.dumps(keep_alive_msg))
                                    logger.info("[Backend->11Labs] Sent keep-alive message")
                                except Exception as e:
                                    logger.error(f"[Backend->11Labs] Error sending keep-alive: {str(e)}")
                                continue

                            if "text" in data and data["text"]:
                                # Handle transcription
                                input_text = data["text"]
                                logger.info(f"[11Labs->Backend] Transcribed: {input_text}")
                                try:
                                    # Get AI response
                                    ai_response = await asyncio.wait_for(
                                        conversation_chain.ainvoke(
                                            {"input": input_text},
                                            config={"configurable": {"session_id": session_id}}
                                        ),
                                        timeout=10.0
                                    )
                                    response_text = ai_response.content
                                    logger.info(f"[Backend] AI Response: {response_text[:100]}...")

                                    # Send response back to ElevenLabs for TTS
                                    if not is_connection_closed(elevenlabs_ws_conn):
                                        synthesis_msg = {
                                            "text": response_text,
                                            "try_trigger_generation": True
                                        }
                                        await elevenlabs_ws_conn.send(json.dumps(synthesis_msg))
                                except asyncio.TimeoutError:
                                    logger.error("AI response timed out")
                                    response_text = "I'm having trouble thinking of a response right now."
                                    if not is_connection_closed(elevenlabs_ws_conn):
                                        await elevenlabs_ws_conn.send(json.dumps({"text": response_text, "try_trigger_generation": True}))
                                except Exception as e:
                                    logger.error(f"Error getting AI response: {str(e)}")
                                    if not is_connection_closed(elevenlabs_ws_conn):
                                        await elevenlabs_ws_conn.send(json.dumps({"text": "Sorry, I encountered an error.", "try_trigger_generation": True}))
                        except json.JSONDecodeError:
                            logger.warning(f"[11Labs->Backend] Invalid JSON: {message}")

                    elif isinstance(message, bytes):
                        # Forward audio data to client
                        logger.info(f"[11Labs->Backend] Forwarding audio chunk ({len(message)} bytes)")
                        try:
                            if client_ws.client_state != WebSocketState.DISCONNECTED:
                                await client_ws.send_bytes(message)
                                logger.info("[Backend->Client] Sent audio to client")
                            else:
                                logger.warning("[Backend->Client] Client disconnected, dropping audio")
                                break
                        except Exception as e:
                            logger.error(f"[Backend->Client] Error sending audio: {str(e)}")
                            break
            except Exception as e:
                logger.error(f"[11Labs->Backend] Error in response handler: {str(e)}")
            finally:
                logger.info("[11Labs->Backend] Response handler finished")
                event.set()

        # Keep-alive task to prevent ElevenLabs timeout
        async def keep_alive_task():
            """Send periodic keep-alive messages to ElevenLabs to prevent timeout"""
            try:
                while not client_disconnected_event.is_set():
                    await asyncio.sleep(15)  # Send keep-alive every 15 seconds
                    if not client_disconnected_event.is_set():
                        try:
                            # Check if connection is still open
                            if hasattr(elevenlabs_ws, 'closed') and not elevenlabs_ws.closed:
                                keep_alive_msg = {"text": " ", "try_trigger_generation": False}
                                await elevenlabs_ws.send(json.dumps(keep_alive_msg))
                                logger.debug("[Backend->11Labs] Sent periodic keep-alive")
                        except Exception as e:
                            logger.debug(f"[Backend->11Labs] Keep-alive failed: {str(e)}")
                            break
            except asyncio.CancelledError:
                logger.debug("[Backend->11Labs] Keep-alive task cancelled")
            except Exception as e:
                logger.error(f"[Backend->11Labs] Keep-alive task error: {str(e)}")

        # Create tasks for handling client and ElevenLabs communication
        client_task = asyncio.create_task(
            handle_client_audio(websocket, elevenlabs_ws, client_disconnected_event),
            name="client_audio_handler"
        )
        elevenlabs_task = asyncio.create_task(
            handle_elevenlabs_response(websocket, elevenlabs_ws, client_disconnected_event),
            name="elevenlabs_response_handler"
        )
        keep_alive_task_obj = asyncio.create_task(
            keep_alive_task(),
            name="elevenlabs_keep_alive"
        )

        # Create a cleanup task
        async def cleanup():
            try:
                # Signal handlers to stop
                client_disconnected_event.set()

                # Cancel tasks
                for task in [client_task, elevenlabs_task, keep_alive_task_obj]:
                    if not task.done():
                        task.cancel()
                        try:
                            await task
                        except (asyncio.CancelledError, Exception) as e:
                            if not isinstance(e, asyncio.CancelledError):
                                logger.error(f"Error in task cleanup: {str(e)}")

                # Close ElevenLabs WebSocket if still open
                if elevenlabs_ws:
                    # Check if connection is closed in a safe way
                    is_closed = True
                    try:
                        # For websockets.WebSocketClientProtocol, check the state properly
                        if hasattr(elevenlabs_ws, 'closed'):
                            is_closed = elevenlabs_ws.closed
                        elif hasattr(elevenlabs_ws, 'state'):
                            # Check if state indicates closed connection
                            is_closed = str(elevenlabs_ws.state) in ['CLOSED', 'CLOSING']
                        else:
                            is_closed = True
                    except Exception:
                        # If we can't access the closed attribute, assume it's closed
                        is_closed = True

                    if not is_closed:
                        try:
                            # One final attempt to send end-of-stream
                            await send_end_of_stream(elevenlabs_ws, "cleanup")
                            # Then close
                            await elevenlabs_ws.close()
                            logger.info("Closed ElevenLabs WebSocket connection")
                        except Exception as e:
                            logger.error(f"[Backend->11Labs] Error closing WebSocket: {str(e)}")

                # Close FastAPI WebSocket if still open
                try:
                    if websocket and websocket.client_state != WebSocketState.DISCONNECTED:
                        await websocket.close()
                        logger.info("Closed client WebSocket connection")
                except Exception as e:
                    logger.error(f"Error closing client WebSocket: {str(e)}")

            except Exception as e:
                logger.error(f"Error during cleanup: {str(e)}")

        try:
            # Wait for either task to complete
            done, pending = await asyncio.wait(
                [client_task, elevenlabs_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Log which task completed
            for task in done:
                if task == client_task:
                    logger.info("Client audio handler completed")
                else:
                    logger.info("ElevenLabs response handler completed")

            # Cancel the other task
            for task in pending:
                task.cancel()

            # Wait for the other task to complete
            if pending:
                await asyncio.wait(pending, timeout=5.0)

        except Exception as e:
            logger.error(f"Error in main WebSocket loop: {str(e)}")
        finally:
            # Ensure cleanup happens
            await cleanup()

    except websockets.exceptions.ConnectionClosedOK:
        logger.info("ElevenLabs WebSocket connection closed gracefully")
    except Exception as e:
        logger.error(f"Error in WebSocket endpoint: {str(e)}", exc_info=True)
    finally:
        # Clean up resources
        try:
            if 'elevenlabs_ws' in locals() and elevenlabs_ws:
                await elevenlabs_ws.close()
                logger.info("Closed ElevenLabs WebSocket connection")
        except Exception as e:
            logger.error(f"Error closing ElevenLabs WebSocket: {str(e)}")

        logger.info("WebSocket endpoint cleanup complete")


# --- Existing HTTP Endpoint (might become less central) --- #

# Removed the HTTP endpoint for now to focus on WebSocket S2S
# @app.post("/speak")
# async def speak(audio: UploadFile = File(...)):
#     logger.info("Received request to /speak endpoint")
#     if not audio.content_type.startswith("audio/"):
#         logger.warning(f"Invalid file type received: {audio.content_type}")
#         raise HTTPException(status_code=400, detail="Invalid file type. Please upload an audio file.")

#     if s2s_provider is None:
#          logger.error("S2S provider not initialized for HTTP endpoint")
#          raise HTTPException(status_code=500, detail="Backend service not fully initialized.")

#     try:
#         audio_input = await audio.read()
#         if not audio_input:
#             logger.warning("Empty audio file received")
#             raise HTTPException(status_code=400, detail="Empty audio file")

#         logger.debug("Processing audio input via HTTP endpoint")
#         # Use the initialized S2S provider (e.g., ElevenLabs HTTP or fallback)
#         audio_output = await s2s_provider.generate_speech(audio_input)
#         logger.info("Successfully processed audio request via HTTP endpoint")
#         return StreamingResponse(iter([audio_output]), media_type="audio/mpeg")
#     except HTTPException:
#         logger.error("HTTPException occurred during audio processing (HTTP endpoint)", exc_info=True)
#         raise
#     except Exception as e:
#         logger.error(f"Audio processing failed (HTTP endpoint): {str(e)}", exc_info=True)
#         raise HTTPException(status_code=500, detail=f"Audio processing failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting FastAPI server")
    uvicorn.run(app, host="0.0.0.0", port=8000)