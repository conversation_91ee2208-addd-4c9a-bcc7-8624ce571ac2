#!/usr/bin/env python3
"""
Simple script to test backend configuration and API connectivity
"""

import os
import yaml
import asyncio
import websockets
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test if required environment variables are set"""
    print("=== Environment Variables Test ===")
    
    google_api_key = os.getenv("GOOGLE_API_KEY")
    elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
    
    print(f"GOOGLE_API_KEY: {'✓ Set' if google_api_key else '✗ Missing'}")
    print(f"ELEVENLABS_API_KEY: {'✓ Set' if elevenlabs_api_key else '✗ Missing'}")
    
    return bool(google_api_key and elevenlabs_api_key)

def test_config():
    """Test if configuration file is valid"""
    print("\n=== Configuration File Test ===")
    
    try:
        with open("config.yaml", "r") as f:
            config = yaml.safe_load(f)
        
        voice_id = config.get("providers", {}).get("elevenlabs", {}).get("voice_id")
        print(f"Config loaded: ✓")
        print(f"Voice ID: {voice_id if voice_id else '✗ Missing'}")
        
        return bool(voice_id)
    except Exception as e:
        print(f"Config error: ✗ {str(e)}")
        return False

async def test_elevenlabs_connection():
    """Test connection to ElevenLabs WebSocket"""
    print("\n=== ElevenLabs WebSocket Test ===")
    
    elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
    if not elevenlabs_api_key:
        print("✗ Cannot test - ELEVENLABS_API_KEY not set")
        return False
    
    try:
        with open("config.yaml", "r") as f:
            config = yaml.safe_load(f)
        
        voice_id = config.get("providers", {}).get("elevenlabs", {}).get("voice_id")
        if not voice_id:
            print("✗ Cannot test - voice_id not configured")
            return False
        
        ws_url = f"wss://api.elevenlabs.io/v1/text-to-speech/{voice_id}/stream-input?model_id=eleven_monolingual_v1"
        print(f"Connecting to: {ws_url}")
        
        # Test connection
        ws = await websockets.connect(
            ws_url,
            additional_headers=[('xi-api-key', elevenlabs_api_key)],
            ping_interval=20,
            ping_timeout=10,
            close_timeout=5
        )
        
        print("✓ Connected to ElevenLabs WebSocket")
        
        # Test initialization message
        init_message = {
            "text": " ",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.8
            }
        }
        
        await ws.send(json.dumps(init_message))
        print("✓ Sent initialization message")
        
        # Wait for a response or timeout
        try:
            response = await asyncio.wait_for(ws.recv(), timeout=5.0)
            print(f"✓ Received response: {type(response).__name__}")
        except asyncio.TimeoutError:
            print("⚠ No response received (this might be normal)")
        
        await ws.close()
        print("✓ Connection closed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Connection failed: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("Backend Configuration Test")
    print("=" * 40)
    
    env_ok = test_environment()
    config_ok = test_config()
    
    if env_ok and config_ok:
        elevenlabs_ok = await test_elevenlabs_connection()
    else:
        print("\n⚠ Skipping ElevenLabs test due to configuration issues")
        elevenlabs_ok = False
    
    print("\n=== Summary ===")
    print(f"Environment: {'✓' if env_ok else '✗'}")
    print(f"Configuration: {'✓' if config_ok else '✗'}")
    print(f"ElevenLabs: {'✓' if elevenlabs_ok else '✗'}")
    
    if env_ok and config_ok and elevenlabs_ok:
        print("\n🎉 All tests passed! Backend should work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        if not env_ok:
            print("   - Create a .env file with your API keys")
        if not config_ok:
            print("   - Check config.yaml file")
        if not elevenlabs_ok:
            print("   - Verify ElevenLabs API key and voice ID")

if __name__ == "__main__":
    asyncio.run(main())
