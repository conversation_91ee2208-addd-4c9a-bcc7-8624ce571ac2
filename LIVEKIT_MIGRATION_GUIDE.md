# LiveKit + <PERSON><PERSON>hai<PERSON> Migration Guide

## 🎯 Why Migrate to LiveKit?

### Current Issues with WebSocket Approach:
- ❌ Connection instability and frequent disconnects
- ❌ Complex audio format handling
- ❌ Manual turn detection and interruption handling
- ❌ ElevenLabs timeout issues
- ❌ No built-in noise cancellation

### Benefits of LiveKit:
- ✅ **WebRTC-based**: Much more reliable than WebSocket
- ✅ **Built-in Turn Detection**: Advanced voice activity detection
- ✅ **Interruption Handling**: Natural conversation flow
- ✅ **Production Ready**: Scaling, load balancing, monitoring
- ✅ **LangChain Integration**: Native support for tools and workflows
- ✅ **Multi-provider Support**: Easy to switch between AI providers

## 🚀 Quick Setup

### 1. LiveKit Server Setup

#### Option A: LiveKit Cloud (Recommended for testing)
1. Sign up at [LiveKit Cloud](https://cloud.livekit.io)
2. Create a new project
3. Get your API key and secret

#### Option B: Self-hosted
```bash
# Using Docker
docker run --rm -p 7880:7880 \
  -e LIVEKIT_KEYS="your_api_key: your_api_secret" \
  livekit/livekit-server
```

### 2. Backend Agent Setup

```bash
# Navigate to agent directory
cd livekit_agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your API keys

# Run the agent
python agent.py dev
```

### 3. Frontend Setup

```bash
# Navigate to frontend directory
cd livekit_frontend

# Install dependencies
flutter pub get

# Update configuration in lib/main.dart
# Set your LiveKit server URL and implement token generation

# Run the app
flutter run
```

## 🔧 Configuration

### Environment Variables (.env)
```env
# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your_livekit_api_key
LIVEKIT_API_SECRET=your_livekit_api_secret

# AI Provider API Keys (reuse from current setup)
GOOGLE_API_KEY=your_google_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
DEEPGRAM_API_KEY=your_deepgram_api_key_here
```

### Token Generation
For production, implement a backend endpoint to generate access tokens:

```python
from livekit import api

def generate_token(room_name: str, participant_identity: str) -> str:
    token = api.AccessToken(api_key, api_secret) \
        .with_identity(participant_identity) \
        .with_name(participant_identity) \
        .with_grants(api.VideoGrants(
            room_join=True,
            room=room_name,
        ))
    return token.to_jwt()
```

## 🎙️ Features Included

### Voice Assistant Capabilities:
- **Natural Conversations**: Automatic turn detection and interruptions
- **LangChain Tools**: Weather, reminders, knowledge search
- **Memory**: Conversation history across sessions
- **Multi-provider AI**: Google Gemini, ElevenLabs TTS, Deepgram STT

### Advanced Features:
- **Noise Cancellation**: Built-in WebRTC audio processing
- **Echo Cancellation**: Automatic feedback prevention
- **Adaptive Bitrate**: Adjusts to network conditions
- **Connection Recovery**: Automatic reconnection on network issues

## 🔄 Migration Steps

### Phase 1: Parallel Testing
1. Keep current WebSocket implementation running
2. Set up LiveKit agent alongside current backend
3. Test LiveKit with a separate Flutter app
4. Compare performance and reliability

### Phase 2: Feature Parity
1. Migrate all LangChain tools to LiveKit agent
2. Implement conversation memory
3. Add any custom business logic
4. Test all use cases

### Phase 3: Full Migration
1. Update main Flutter app to use LiveKit
2. Deprecate WebSocket backend
3. Monitor performance and user feedback
4. Optimize and scale as needed

## 🛠️ Customization

### Adding New LangChain Tools:
```python
@tool
def custom_tool(parameter: str) -> str:
    """Description of what the tool does."""
    # Your implementation here
    return result

# Add to tools list in agent.py
tools = [get_weather, set_reminder, search_knowledge, custom_tool]
```

### Changing AI Providers:
```python
# Replace Deepgram with OpenAI Whisper
stt=openai.STT(),

# Replace ElevenLabs with OpenAI TTS
tts=openai.TTS(voice="alloy"),

# Use different LLM
llm=openai.LLM(model="gpt-4"),
```

## 📊 Performance Comparison

| Feature | Current WebSocket | LiveKit |
|---------|------------------|---------|
| Connection Stability | ❌ Poor | ✅ Excellent |
| Audio Quality | ⚠️ Variable | ✅ High |
| Turn Detection | ❌ Manual | ✅ Automatic |
| Interruption Handling | ❌ None | ✅ Natural |
| Noise Cancellation | ⚠️ Basic | ✅ Advanced |
| Scalability | ❌ Limited | ✅ Production-ready |
| Development Complexity | ❌ High | ✅ Low |

## 🚀 Production Deployment

### Docker Deployment:
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "agent.py", "start"]
```

### Kubernetes Deployment:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: voice-assistant-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: voice-assistant-agent
  template:
    metadata:
      labels:
        app: voice-assistant-agent
    spec:
      containers:
      - name: agent
        image: your-registry/voice-assistant-agent:latest
        env:
        - name: LIVEKIT_URL
          value: "wss://your-livekit-server.com"
        - name: LIVEKIT_API_KEY
          valueFrom:
            secretKeyRef:
              name: livekit-secrets
              key: api-key
```

## 🔍 Monitoring and Debugging

### Built-in Metrics:
- Connection quality
- Audio latency
- Turn detection accuracy
- LLM response times

### Logging:
```python
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("voice-assistant")
```

## 🎯 Next Steps

1. **Set up LiveKit Cloud account** (5 minutes)
2. **Run the agent locally** (10 minutes)
3. **Test with Flutter app** (15 minutes)
4. **Compare with current implementation** (30 minutes)
5. **Plan full migration** (based on results)

The LiveKit approach will solve all the current WebSocket issues and provide a much more robust, scalable solution for your AI voice companion! 🎉
