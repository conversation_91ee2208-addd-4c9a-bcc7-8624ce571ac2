<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple LiveKit Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 15px 30px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .logs {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Simple LiveKit Test</h1>
        <p>Test WebSocket connection to LiveKit</p>
        
        <div id="status" class="status">
            Status: Ready to test
        </div>
        
        <button onclick="testConnection()">Test Connection</button>
        
        <div class="logs" id="logs">
            <div>Ready to test LiveKit connection...</div>
        </div>
    </div>

    <script>
        const LIVEKIT_URL = 'wss://test-6hztxoof.livekit.cloud';
        const LIVEKIT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiVGVzdCBVc2VyIiwidmlkZW8iOnsicm9vbUpvaW4iOnRydWUsInJvb20iOiJ0ZXN0LXJvb20iLCJjYW5QdWJsaXNoIjp0cnVlLCJjYW5TdWJzY3JpYmUiOnRydWUsImNhblB1Ymxpc2hEYXRhIjp0cnVlfSwic3ViIjoidGVzdC11c2VyIiwiaXNzIjoiQVBJZGRMM3RjRnV1TjRLIiwibmJmIjoxNzQ4MTE1OTc5LCJleHAiOjE3NDgxMzc1Nzl9.wcv96vKo1ZEJyC3Nlz5Scd8BmRb8tXB1kMgUT6-lDQU';
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = `Status: ${message}`;
        }
        
        async function testConnection() {
            try {
                log('🚀 Testing WebSocket connection to LiveKit...');
                updateStatus('Testing connection...');
                
                // Test basic WebSocket connection
                const ws = new WebSocket(LIVEKIT_URL);
                
                ws.onopen = function() {
                    log('✅ WebSocket connection opened successfully!');
                    updateStatus('WebSocket Connected');
                    
                    // Send a basic message
                    setTimeout(() => {
                        log('📤 Sending test message...');
                        ws.send('test');
                    }, 1000);
                };
                
                ws.onmessage = function(event) {
                    log(`📥 Received message: ${event.data}`);
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`);
                    updateStatus('Connection Error');
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket closed: Code ${event.code}, Reason: ${event.reason}`);
                    updateStatus('Connection Closed');
                };
                
                // Close after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        log('🔌 Closing test connection...');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`);
                updateStatus('Test Failed');
                console.error('Test error:', error);
            }
        }
        
        // Initialize
        log('🔧 Simple test page loaded');
        log(`📡 Target URL: ${LIVEKIT_URL}`);
        log('💡 Click "Test Connection" to verify WebSocket connectivity');
    </script>
</body>
</html>
