#!/usr/bin/env python3
"""
LiveKit Voice AI Agent with LangChain Integration
Complete voice AI companion using LiveKit Agents framework
"""

import asyncio
import logging
import os
from typing import AsyncIterable

from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
    stt,
    tts,
    vad,
)
from livekit.plugins import deepgram, elevenlabs, silero
from livekit import rtc

# Lang<PERSON>hain imports
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.tools import tool
from langchain.agents import create_tool_calling_agent, AgentExecutor

# Environment setup
from dotenv import load_dotenv
load_dotenv()

logger = logging.getLogger("voice-assistant")
logger.setLevel(logging.INFO)

# Store for conversation histories
store = {}

def get_session_history(session_id: str) -> ChatMessageHistory:
    if session_id not in store:
        store[session_id] = ChatMessageHistory()
    return store[session_id]

# Define LangChain tools
@tool
def get_weather(location: str) -> str:
    """Get the current weather for a location."""
    # Mock implementation - replace with real weather API
    return f"The weather in {location} is sunny and 72°F"

@tool
def set_reminder(reminder: str, time: str) -> str:
    """Set a reminder for the user."""
    # Mock implementation - replace with real reminder system
    return f"Reminder set: '{reminder}' at {time}"

@tool
def search_knowledge(query: str) -> str:
    """Search the knowledge base for information."""
    # Mock implementation - replace with real knowledge base
    return f"Found information about: {query}"

@tool
def get_time() -> str:
    """Get the current time."""
    import datetime
    return f"The current time is {datetime.datetime.now().strftime('%I:%M %p')}"

class LangChainLLM(llm.LLM):
    """Custom LLM wrapper for LangChain integration"""

    def __init__(self, session_id: str):
        super().__init__()
        self.session_id = session_id

        # Initialize Google Gemini LLM
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            temperature=0.7
        )

        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a helpful AI voice assistant. You can:
            - Answer questions and have conversations
            - Get weather information
            - Set reminders
            - Search knowledge bases

            Be conversational, helpful, and concise in your responses since this is a voice conversation.
            """),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])

        # Create tools list
        tools = [get_weather, set_reminder, search_knowledge]

        # Create agent
        agent = create_tool_calling_agent(self.llm, tools, prompt)

        # Create agent executor with memory
        self.agent_executor = RunnableWithMessageHistory(
            AgentExecutor(agent=agent, tools=tools, verbose=True),
            get_session_history,
            input_messages_key="input",
            history_messages_key="chat_history",
        )

    async def agenerate(
        self,
        *,
        message: str,
        **kwargs,
    ) -> llm.LLMStream:
        """Generate response using LangChain agent"""

        # Run the agent with conversation history
        response = await self.agent_executor.ainvoke(
            {"input": message},
            config={"configurable": {"session_id": self.session_id}}
        )

        # Extract the output text
        output_text = response.get("output", "I'm sorry, I couldn't process that.")

        # Create a simple stream that yields the complete response
        class SimpleStream(llm.LLMStream):
            def __init__(self, text: str):
                super().__init__()
                self._text = text
                self._sent = False

            async def __anext__(self) -> llm.ChatChunk:
                if self._sent:
                    raise StopAsyncIteration
                self._sent = True
                return llm.ChatChunk(
                    choices=[
                        llm.Choice(
                            delta=llm.ChoiceDelta(content=self._text, role="assistant"),
                            index=0
                        )
                    ]
                )

        return SimpleStream(output_text)

class VoiceAssistant:
    """Manual Voice Assistant implementation"""

    def __init__(self, room: rtc.Room, session_id: str):
        self.room = room
        self.session_id = session_id
        self.vad = silero.VAD.load()
        self.stt = deepgram.STT()
        self.llm = LangChainLLM(session_id)
        self.tts = elevenlabs.TTS(voice="At0PgxDCGLVlDr0N86Ty")
        self.is_listening = False

    async def start(self):
        """Start the voice assistant"""
        logger.info("🎤 Voice assistant started")

        # Set up room event handlers
        @self.room.on("track_subscribed")
        def on_track_subscribed(track: rtc.Track, publication: rtc.TrackPublication, participant: rtc.RemoteParticipant):
            if track.kind == rtc.TrackKind.KIND_AUDIO:
                logger.info(f"🎵 Audio track subscribed from {participant.identity}")
                asyncio.create_task(self._handle_audio_track(track))

        # Say hello
        await self._say("Hello! I'm your AI voice assistant. How can I help you today?")

    async def _handle_audio_track(self, track: rtc.AudioTrack):
        """Handle incoming audio track"""
        logger.info("🎧 Starting audio processing...")

        audio_stream = rtc.AudioStream(track)

        async for audio_frame_event in audio_stream:
            # Voice activity detection
            vad_result = await self.vad.analyze_frame(audio_frame_event.frame)

            if vad_result.speech_detected and not self.is_listening:
                logger.info("🗣️ Speech detected, starting transcription...")
                self.is_listening = True
                asyncio.create_task(self._process_speech(audio_stream))

    async def _process_speech(self, audio_stream):
        """Process speech input"""
        try:
            # Collect audio for transcription
            audio_buffer = []
            speech_timeout = 2.0  # seconds of silence to end speech
            last_speech_time = asyncio.get_event_loop().time()

            async for audio_frame_event in audio_stream:
                audio_buffer.append(audio_frame_event.frame)

                # Check for end of speech
                vad_result = await self.vad.analyze_frame(audio_frame_event.frame)
                if vad_result.speech_detected:
                    last_speech_time = asyncio.get_event_loop().time()
                elif asyncio.get_event_loop().time() - last_speech_time > speech_timeout:
                    break

            if audio_buffer:
                # Transcribe speech
                logger.info("📝 Transcribing speech...")
                transcription = await self.stt.recognize(audio_buffer)

                if transcription.text.strip():
                    logger.info(f"👤 User said: {transcription.text}")

                    # Generate response
                    logger.info("🤖 Generating AI response...")
                    response_stream = await self.llm.agenerate(message=transcription.text)

                    response_text = ""
                    async for chunk in response_stream:
                        if chunk.choices and chunk.choices[0].delta.content:
                            response_text += chunk.choices[0].delta.content

                    if response_text.strip():
                        logger.info(f"🤖 AI response: {response_text}")
                        await self._say(response_text)

        except Exception as e:
            logger.error(f"❌ Error processing speech: {e}")
        finally:
            self.is_listening = False

    async def _say(self, text: str):
        """Convert text to speech and play it"""
        try:
            logger.info(f"🔊 Speaking: {text}")

            # Generate audio
            audio_stream = self.tts.synthesize(text)

            # Create audio source and track
            audio_source = rtc.AudioSource(sample_rate=24000, num_channels=1)
            track = rtc.LocalAudioTrack.create_audio_track("assistant-voice", audio_source)

            # Publish the track
            await self.room.local_participant.publish_track(track, rtc.TrackPublishOptions())

            # Stream the audio
            async for audio_frame in audio_stream:
                await audio_source.capture_frame(audio_frame)

        except Exception as e:
            logger.error(f"❌ Error in text-to-speech: {e}")

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent"""

    logger.info("🚀 Starting voice AI agent...")

    # Wait for the first participant to connect
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    # Get session ID from room name
    session_id = ctx.room.name or "default_session"

    logger.info(f"✅ Connected to room: {ctx.room.name}")
    logger.info(f"🆔 Session ID: {session_id}")

    # Create and start voice assistant
    assistant = VoiceAssistant(ctx.room, session_id)
    await assistant.start()

    logger.info("🎯 Voice AI agent is ready!")

    # Keep the agent running
    while True:
        await asyncio.sleep(5)
        participant_count = len(ctx.room.remote_participants)
        logger.info(f"💓 Agent heartbeat - {participant_count} participants connected")

if __name__ == "__main__":
    # Configure worker options
    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
        prewarm_fnc=None,  # Optional: preload models
    )

    # Start the agent
    cli.run_app(worker_options)
