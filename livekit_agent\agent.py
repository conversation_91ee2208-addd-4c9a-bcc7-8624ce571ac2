#!/usr/bin/env python3
"""
LiveKit Agent with LangChain Integration
Advanced voice AI companion using LiveKit Agents framework
"""

import asyncio
import logging
import os
from typing import Annotated

from livekit import agents, rtc
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
    stt,
    tts,
    vad,
)
from livekit.agents.voice_assistant import VoiceAssistant
from livekit.plugins import deepgram, elevenlabs, openai, silero

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.tools import tool
from langchain.agents import create_tool_calling_agent, AgentExecutor

# Environment setup
from dotenv import load_dotenv
load_dotenv()

logger = logging.getLogger("voice-assistant")
logger.setLevel(logging.INFO)

# Store for conversation histories
store = {}

def get_session_history(session_id: str) -> ChatMessageHistory:
    if session_id not in store:
        store[session_id] = ChatMessageHistory()
    return store[session_id]

# Define LangChain tools
@tool
def get_weather(location: str) -> str:
    """Get the current weather for a location."""
    # Mock implementation - replace with real weather API
    return f"The weather in {location} is sunny and 72°F"

@tool
def set_reminder(reminder: str, time: str) -> str:
    """Set a reminder for the user."""
    # Mock implementation - replace with real reminder system
    return f"Reminder set: '{reminder}' at {time}"

@tool
def search_knowledge(query: str) -> str:
    """Search the knowledge base for information."""
    # Mock implementation - replace with real knowledge base
    return f"Found information about: {query}"

class LangChainLLM(llm.LLM):
    """Custom LLM wrapper for LangChain integration"""
    
    def __init__(self, session_id: str):
        super().__init__()
        self.session_id = session_id
        
        # Initialize Google Gemini LLM
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            temperature=0.7
        )
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a helpful AI voice assistant. You can:
            - Answer questions and have conversations
            - Get weather information
            - Set reminders
            - Search knowledge bases
            
            Be conversational, helpful, and concise in your responses since this is a voice conversation.
            """),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
        
        # Create tools list
        tools = [get_weather, set_reminder, search_knowledge]
        
        # Create agent
        agent = create_tool_calling_agent(self.llm, tools, prompt)
        
        # Create agent executor with memory
        self.agent_executor = RunnableWithMessageHistory(
            AgentExecutor(agent=agent, tools=tools, verbose=True),
            get_session_history,
            input_messages_key="input",
            history_messages_key="chat_history",
        )
    
    async def agenerate(
        self,
        *,
        message: str,
        **kwargs,
    ) -> llm.LLMStream:
        """Generate response using LangChain agent"""
        
        # Run the agent with conversation history
        response = await self.agent_executor.ainvoke(
            {"input": message},
            config={"configurable": {"session_id": self.session_id}}
        )
        
        # Extract the output text
        output_text = response.get("output", "I'm sorry, I couldn't process that.")
        
        # Create a simple stream that yields the complete response
        class SimpleStream(llm.LLMStream):
            def __init__(self, text: str):
                super().__init__()
                self._text = text
                self._sent = False
            
            async def __anext__(self) -> llm.ChatChunk:
                if self._sent:
                    raise StopAsyncIteration
                self._sent = True
                return llm.ChatChunk(
                    choices=[
                        llm.Choice(
                            delta=llm.ChoiceDelta(content=self._text, role="assistant"),
                            index=0
                        )
                    ]
                )
        
        return SimpleStream(output_text)

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent"""
    
    # Wait for the first participant to connect
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    
    # Get session ID from room name or participant
    session_id = ctx.room.name or "default_session"
    
    logger.info(f"Starting voice assistant for session: {session_id}")
    
    # Initialize AI components
    initial_ctx = llm.ChatContext().append(
        role="assistant",
        text="Hello! I'm your AI voice assistant. How can I help you today?",
    )
    
    # Create voice assistant with LangChain integration
    assistant = VoiceAssistant(
        vad=silero.VAD.load(),  # Voice activity detection
        stt=deepgram.STT(),     # Speech-to-text
        llm=LangChainLLM(session_id),  # Our custom LangChain LLM
        tts=elevenlabs.TTS(voice="At0PgxDCGLVlDr0N86Ty"),  # Text-to-speech
        chat_ctx=initial_ctx,
    )
    
    # Start the assistant
    assistant.start(ctx.room)
    
    # Keep the agent running
    await asyncio.sleep(1)
    await assistant.say("Hello! I'm your AI voice assistant. How can I help you today?", allow_interruptions=True)

if __name__ == "__main__":
    # Configure worker options
    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
        prewarm_fnc=None,  # Optional: preload models
    )
    
    # Start the agent
    cli.run_app(worker_options)
