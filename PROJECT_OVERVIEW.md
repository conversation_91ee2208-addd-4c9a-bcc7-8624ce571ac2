# Real-Time Conversational AI Companion

## Overview
This project is a real-time conversational AI system that enables seamless, natural speech-to-speech (S2S) interaction between a user and an AI assistant. The system leverages a modular backend with FastAPI, LangChain (using <PERSON> as the LLM), and ElevenLabs for speech synthesis and transcription, paired with a Flutter frontend for cross-platform user experience.

## Features
- **Real-Time Speech-to-Speech AI Dialog:** Users speak to the app and receive expressive, natural audio responses from the AI.
- **Conversational Memory:** Maintains context and history for coherent, multi-turn conversations using LangChain.
- **Modular S2S Backend:** Easily swap S2S providers (e.g., ElevenLabs, Gemini Native Audio) with minimal code changes.
- **Streaming Audio:** Audio is streamed in real time between client and backend for low-latency interaction.
- **Cross-Platform Frontend:** Flutter app for Android, iOS, desktop, and web.

## Architecture

### 1. **Frontend (Flutter App)**
- Records user audio (microphone input).
- Streams audio to backend via WebSocket.
- Receives and plays back audio responses from backend.
- Displays conversation status and controls.

### 2. **Backend (Python, FastAPI)**
- Receives audio from <PERSON>lutter app over WebSocket.
- Uses <PERSON><PERSON>hain for conversational memory and LLM (Gemini) responses.
- Forwards audio and context to ElevenLabs for transcription and speech synthesis.
- Streams synthesized audio back to the client in real time.
- Handles session management and robust cleanup.

## Technology Stack
- **Frontend:** Flutter, Dart, just_audio, record, web_socket_channel
- **Backend:** Python, FastAPI, LangChain, Gemini (Google Generative AI), ElevenLabs, websockets, asyncio
- **Other:** Docker (for deployment), YAML (for config), dotenv (for secrets)

## Process / Workflow
1. **User speaks into the Flutter app.**
2. **Audio is streamed to the backend** via WebSocket.
3. **Backend forwards audio to ElevenLabs** for transcription.
4. **Transcribed text is sent to LangChain (Gemini)** for AI response, maintaining conversation history.
5. **AI response is sent back to ElevenLabs** for speech synthesis.
6. **Synthesized audio is streamed back to the client** and played in the app.
7. **Session and memory are managed** for each user connection.

## Key Implementation Details
- **WebSocket Endpoint:** `/ws/audio` for real-time, bidirectional audio and control messages.
- **Session Memory:** Per-connection conversational memory using LangChain's `RunnableWithMessageHistory` and `ChatMessageHistory`.
- **End-of-Stream Handling:** Ensures ElevenLabs receives the required end-of-stream message to avoid policy violations.
- **Error Handling:** Robust cleanup and error logging for all connection and processing steps.

## Next Steps
- Polish client and backend error handling and reconnection logic.
- (Optional) Add support for additional S2S providers (e.g., Gemini Native Audio).
- (Optional) Enhance UI/UX for conversation history and controls.
- Prepare for production deployment (Docker, environment config, etc).

---

For further details, see the codebase or request specific implementation examples. 