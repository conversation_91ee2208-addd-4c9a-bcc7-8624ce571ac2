["C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.worklet.js", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.fixwebmduration.js", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\Development\\sol-ai\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]