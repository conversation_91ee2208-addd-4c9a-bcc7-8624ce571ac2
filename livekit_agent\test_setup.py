#!/usr/bin/env python3
"""
Test script to verify LiveKit Agent setup
"""

import os
import sys
from dotenv import load_dotenv

def test_environment():
    """Test environment variables"""
    print("=== Environment Variables Test ===")
    
    load_dotenv()
    
    google_api_key = os.getenv("GOOGLE_API_KEY")
    elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
    livekit_url = os.getenv("LIVEKIT_URL")
    livekit_api_key = os.getenv("LIVEKIT_API_KEY")
    livekit_api_secret = os.getenv("LIVEKIT_API_SECRET")
    
    print(f"GOOGLE_API_KEY: {'✓ Set' if google_api_key else '✗ Missing'}")
    print(f"ELEVENLABS_API_KEY: {'✓ Set' if elevenlabs_api_key else '✗ Missing'}")
    print(f"LIVEKIT_URL: {'✓ Set' if livekit_url else '✗ Missing'}")
    print(f"LIVEKIT_API_KEY: {'✓ Set' if livekit_api_key else '✗ Missing'}")
    print(f"LIVEKIT_API_SECRET: {'✓ Set' if livekit_api_secret else '✗ Missing'}")
    
    return all([google_api_key, elevenlabs_api_key, livekit_url, livekit_api_key, livekit_api_secret])

def test_imports():
    """Test if all required packages can be imported"""
    print("\n=== Package Import Test ===")
    
    packages = [
        ("livekit.agents", "LiveKit Agents"),
        ("livekit.plugins.deepgram", "Deepgram Plugin"),
        ("livekit.plugins.elevenlabs", "ElevenLabs Plugin"),
        ("livekit.plugins.silero", "Silero Plugin"),
        ("langchain_google_genai", "LangChain Google GenAI"),
        ("dotenv", "Python Dotenv"),
    ]
    
    all_imported = True
    
    for package, name in packages:
        try:
            __import__(package)
            print(f"{name}: ✓ Available")
        except ImportError as e:
            print(f"{name}: ✗ Missing ({str(e)})")
            all_imported = False
    
    return all_imported

def test_livekit_connection():
    """Test LiveKit connection (basic validation)"""
    print("\n=== LiveKit Configuration Test ===")
    
    load_dotenv()
    
    livekit_url = os.getenv("LIVEKIT_URL")
    livekit_api_key = os.getenv("LIVEKIT_API_KEY")
    livekit_api_secret = os.getenv("LIVEKIT_API_SECRET")
    
    if not all([livekit_url, livekit_api_key, livekit_api_secret]):
        print("✗ LiveKit credentials not configured")
        return False
    
    # Basic URL validation
    if not livekit_url.startswith("wss://"):
        print("✗ LiveKit URL should start with wss://")
        return False
    
    if "your_" in livekit_api_key or "your_" in livekit_api_secret:
        print("✗ LiveKit credentials still contain placeholder values")
        return False
    
    print("✓ LiveKit credentials appear to be configured")
    print(f"  URL: {livekit_url}")
    print(f"  API Key: {livekit_api_key[:8]}...")
    
    return True

def main():
    """Run all tests"""
    print("LiveKit Agent Setup Test")
    print("=" * 40)
    
    env_ok = test_environment()
    imports_ok = test_imports()
    livekit_ok = test_livekit_connection()
    
    print("\n=== Summary ===")
    print(f"Environment: {'✓' if env_ok else '✗'}")
    print(f"Imports: {'✓' if imports_ok else '✗'}")
    print(f"LiveKit Config: {'✓' if livekit_ok else '✗'}")
    
    if env_ok and imports_ok and livekit_ok:
        print("\n🎉 All tests passed! Ready to run the LiveKit agent.")
        print("\nNext steps:")
        print("1. Make sure you have a LiveKit Cloud project set up")
        print("2. Update the .env file with your actual LiveKit credentials")
        print("3. Run: python simple_agent.py dev")
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")
        if not env_ok:
            print("   - Check your .env file")
        if not imports_ok:
            print("   - Install missing packages")
        if not livekit_ok:
            print("   - Set up LiveKit Cloud and update credentials")

if __name__ == "__main__":
    main()
