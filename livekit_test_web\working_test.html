<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit Agent Test - Working</title>
    <script src="https://unpkg.com/livekit-client@1.15.13/dist/livekit-client.umd.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.disconnected {
            background-color: #ffebee;
            color: #c62828;
        }
        .status.connecting {
            background-color: #fff3e0;
            color: #ef6c00;
        }
        .status.connected {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        button {
            padding: 15px 30px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .connect-btn {
            background-color: #4caf50;
            color: white;
        }
        .disconnect-btn {
            background-color: #f44336;
            color: white;
        }
        .logs {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 LiveKit Agent Test</h1>
        <p>Test connection to your LiveKit agent</p>
        
        <div id="status" class="status disconnected">
            Status: Disconnected
        </div>
        
        <button id="connectBtn" class="connect-btn" onclick="connectToRoom()">
            Connect to Agent
        </button>
        
        <button id="disconnectBtn" class="disconnect-btn" onclick="disconnectFromRoom()" style="display: none;">
            Disconnect
        </button>
        
        <div class="logs" id="logs">
            <div>Ready to connect...</div>
        </div>
    </div>

    <script>
        // LiveKit configuration - using the fresh token
        const LIVEKIT_URL = 'wss://test-6hztxoof.livekit.cloud';
        const LIVEKIT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiVGVzdCBVc2VyIiwidmlkZW8iOnsicm9vbUpvaW4iOnRydWUsInJvb20iOiJ0ZXN0LXJvb20iLCJjYW5QdWJsaXNoIjp0cnVlLCJjYW5TdWJzY3JpYmUiOnRydWUsImNhblB1Ymxpc2hEYXRhIjp0cnVlfSwic3ViIjoidGVzdC11c2VyIiwiaXNzIjoiQVBJZGRMM3RjRnV1TjRLIiwibmJmIjoxNzQ4MTE2Mjc0LCJleHAiOjE3NDgxMzc4NzR9.wdfT0R0fPTEgkm-H51hvQQGWz-bS6MQT_vergSpdfbQ';
        
        let room = null;
        let isConnected = false;
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = `Status: ${status}`;
            statusEl.className = `status ${className}`;
        }
        
        function updateButtons() {
            document.getElementById('connectBtn').style.display = isConnected ? 'none' : 'inline-block';
            document.getElementById('disconnectBtn').style.display = isConnected ? 'inline-block' : 'none';
        }
        
        async function connectToRoom() {
            try {
                log('🚀 Starting connection to LiveKit...');
                updateStatus('Connecting...', 'connecting');
                
                // Check if LiveKit is available
                if (typeof LiveKit === 'undefined') {
                    throw new Error('LiveKit client library not loaded');
                }
                
                log('📚 LiveKit library loaded successfully');
                
                // Create room
                room = new LiveKit.Room({
                    adaptiveStream: true,
                    dynacast: true,
                });
                
                log('🏠 Room instance created');
                
                // Set up event listeners
                room.on(LiveKit.RoomEvent.Connected, () => {
                    log('✅ Connected to LiveKit room!');
                    updateStatus('Connected', 'connected');
                    isConnected = true;
                    updateButtons();
                });
                
                room.on(LiveKit.RoomEvent.Disconnected, (reason) => {
                    log(`👋 Disconnected from room: ${reason}`);
                    updateStatus('Disconnected', 'disconnected');
                    isConnected = false;
                    updateButtons();
                });
                
                room.on(LiveKit.RoomEvent.ParticipantConnected, (participant) => {
                    log(`👤 Participant connected: ${participant.identity}`);
                });
                
                room.on(LiveKit.RoomEvent.TrackSubscribed, (track, publication, participant) => {
                    log(`🎵 Track subscribed: ${track.kind} from ${participant.identity}`);
                });
                
                room.on(LiveKit.RoomEvent.ConnectionStateChanged, (state) => {
                    log(`🔄 Connection state: ${state}`);
                });
                
                // Connect to room
                log('🔌 Attempting to connect...');
                await room.connect(LIVEKIT_URL, LIVEKIT_TOKEN);
                
                log('🎯 Connection successful! Check agent logs for activity.');
                
            } catch (error) {
                log(`❌ Connection failed: ${error.message}`);
                updateStatus('Connection Failed', 'disconnected');
                console.error('Connection error:', error);
            }
        }
        
        async function disconnectFromRoom() {
            if (room) {
                log('🔌 Disconnecting from room...');
                await room.disconnect();
                room = null;
            }
        }
        
        // Initialize
        log('🔧 LiveKit test page loaded');
        log(`📡 Target URL: ${LIVEKIT_URL}`);
        log('💡 Click "Connect to Agent" to test the connection');
        
        // Check if LiveKit is loaded
        setTimeout(() => {
            if (typeof LiveKit !== 'undefined') {
                log('✅ LiveKit client library loaded successfully');
            } else {
                log('❌ LiveKit client library failed to load');
            }
        }, 1000);
    </script>
</body>
</html>
