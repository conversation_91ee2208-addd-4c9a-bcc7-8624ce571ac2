#!/usr/bin/env python3
"""
Quick test to verify basic setup
"""

print("Testing basic imports...")

try:
    import os
    print("✓ os module")
except ImportError as e:
    print(f"✗ os module: {e}")

try:
    from dotenv import load_dotenv
    print("✓ python-dotenv")
except ImportError as e:
    print(f"✗ python-dotenv: {e}")

try:
    import livekit
    print("✓ livekit")
except ImportError as e:
    print(f"✗ livekit: {e}")

try:
    from livekit import agents
    print("✓ livekit.agents")
except ImportError as e:
    print(f"✗ livekit.agents: {e}")

try:
    from livekit.plugins import elevenlabs
    print("✓ livekit.plugins.elevenlabs")
except ImportError as e:
    print(f"✗ livekit.plugins.elevenlabs: {e}")

try:
    from livekit.plugins import deepgram
    print("✓ livekit.plugins.deepgram")
except ImportError as e:
    print(f"✗ livekit.plugins.deepgram: {e}")

try:
    from livekit.plugins import silero
    print("✓ livekit.plugins.silero")
except ImportError as e:
    print(f"✗ livekit.plugins.silero: {e}")

try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    print("✓ langchain_google_genai")
except ImportError as e:
    print(f"✗ langchain_google_genai: {e}")

print("\nTesting environment variables...")

load_dotenv()

google_key = os.getenv("GOOGLE_API_KEY")
elevenlabs_key = os.getenv("ELEVENLABS_API_KEY")
livekit_url = os.getenv("LIVEKIT_URL")
livekit_key = os.getenv("LIVEKIT_API_KEY")
livekit_secret = os.getenv("LIVEKIT_API_SECRET")

print(f"GOOGLE_API_KEY: {'✓ Set' if google_key else '✗ Missing'}")
print(f"ELEVENLABS_API_KEY: {'✓ Set' if elevenlabs_key else '✗ Missing'}")
print(f"LIVEKIT_URL: {'✓ Set' if livekit_url else '✗ Missing'}")
print(f"LIVEKIT_API_KEY: {'✓ Set' if livekit_key else '✗ Missing'}")
print(f"LIVEKIT_API_SECRET: {'✓ Set' if livekit_secret else '✗ Missing'}")

if livekit_url and "your_" not in livekit_url:
    print(f"LiveKit URL: {livekit_url}")

print("\nSetup status:")
if all([google_key, elevenlabs_key, livekit_url, livekit_key, livekit_secret]):
    if "your_" not in livekit_key and "your_" not in livekit_secret:
        print("🎉 All credentials configured! Ready to test LiveKit agent.")
    else:
        print("⚠️  LiveKit credentials still contain placeholders. Please update with real values.")
else:
    print("❌ Some credentials are missing. Please check your .env file.")

print("\nNext steps:")
print("1. Get your LiveKit Cloud credentials from https://cloud.livekit.io")
print("2. Update the .env file with real values")
print("3. Run: python simple_agent.py dev")
