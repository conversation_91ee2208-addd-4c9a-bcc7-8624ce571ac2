#!/usr/bin/env python3
"""
Working Voice AI Agent
Simple implementation that actually works
"""

import asyncio
import logging
import os

from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
)
from livekit.plugins import deepgram, elevenlabs, openai, silero

# Environment setup
from dotenv import load_dotenv
load_dotenv()

logger = logging.getLogger("working-agent")
logger.setLevel(logging.INFO)

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent"""

    logger.info("🚀 Starting working voice AI agent...")

    # Wait for the first participant to connect
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    logger.info(f"✅ Connected to room: {ctx.room.name}")

    try:
        logger.info("🔧 Initializing AI components...")

        # Use OpenAI for simpler setup
        initial_ctx = llm.ChatContext()
        initial_ctx.messages.append(
            llm.ChatMessage(
                role="assistant",
                content="You are a helpful AI voice assistant. Respond naturally and conversationally.",
            )
        )

        # Try to import and use VoiceAssistant
        try:
            from livekit.agents.voice_assistant import VoiceAssistant

            # Create voice assistant with OpenAI (more reliable)
            assistant = VoiceAssistant(
                vad=silero.VAD.load(),
                stt=deepgram.STT(),
                llm=openai.LLM(model="gpt-3.5-turbo"),
                tts=elevenlabs.TTS(voice="At0PgxDCGLVlDr0N86Ty"),
                chat_ctx=initial_ctx,
            )

            logger.info("✅ VoiceAssistant created successfully!")

            # Start the assistant
            assistant.start(ctx.room)

            logger.info("🎯 Voice assistant started!")

            # Say hello after a short delay
            await asyncio.sleep(2)
            await assistant.say("Hello! I'm your AI voice assistant. I can hear you clearly now. Try saying something to me!", allow_interruptions=True)

        except ImportError as e:
            logger.error(f"❌ VoiceAssistant not available: {e}")
            logger.info("🔄 Using basic audio handling...")

            # Basic fallback - just log connections
            @ctx.room.on("participant_connected")
            def on_participant_connected(participant):
                logger.info(f"👤 Participant connected: {participant.identity}")

            @ctx.room.on("track_subscribed")
            def on_track_subscribed(track, publication, participant):
                logger.info(f"🎵 Audio track from {participant.identity}")

    except Exception as e:
        logger.error(f"❌ Error setting up voice assistant: {e}")
        import traceback
        traceback.print_exc()

    # Keep the agent running
    while True:
        await asyncio.sleep(5)
        participant_count = len(ctx.room.remote_participants)
        if participant_count > 0:
            logger.info(f"💓 Agent active - {participant_count} participants connected")

if __name__ == "__main__":
    # Configure worker options
    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
    )

    logger.info("🔧 Starting working voice agent...")

    # Start the agent
    cli.run_app(worker_options)
