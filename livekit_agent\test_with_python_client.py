#!/usr/bin/env python3
"""
Test LiveKit connection using Python client
This will simulate a client connecting to test the agent
"""

import os
import asyncio
import logging
from livekit import rtc, api
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_client_connection():
    """Test connecting as a client to the LiveKit room"""
    
    load_dotenv()
    
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    livekit_url = os.getenv("LIVEKIT_URL")
    
    print("🔧 Testing LiveKit Client Connection")
    print("=" * 50)
    print(f"URL: {livekit_url}")
    
    # Generate access token
    print("🎫 Generating access token...")
    token = api.AccessToken(api_key, api_secret) \
        .with_identity("python-test-client") \
        .with_name("Python Test Client") \
        .with_grants(api.VideoGrants(
            room_join=True,
            room="test-room",
            can_publish=True,
            can_subscribe=True,
            can_publish_data=True,
        ))
    
    jwt_token = token.to_jwt()
    print(f"✅ Token generated")
    
    try:
        print("\n🚀 Connecting to LiveKit room...")
        
        # Create room
        room = rtc.Room()
        
        # Set up event handlers
        @room.on("connected")
        def on_connected():
            print("✅ Connected to LiveKit room!")
            print(f"   Room name: {room.name}")
            print(f"   Local participant: {room.local_participant.identity}")
        
        @room.on("disconnected")
        def on_disconnected(reason):
            print(f"👋 Disconnected: {reason}")
        
        @room.on("participant_connected")
        def on_participant_connected(participant):
            print(f"👤 Participant connected: {participant.identity}")
        
        @room.on("track_subscribed")
        def on_track_subscribed(track, publication, participant):
            print(f"🎵 Track subscribed: {track.kind} from {participant.identity}")
        
        # Connect to room
        await room.connect(livekit_url, jwt_token)
        
        print("🎯 Connection successful!")
        print("📱 This should trigger the agent to join the room")
        print("🔍 Check the agent terminal for activity...")
        
        # Stay connected for a bit to see agent activity
        print("\n⏳ Staying connected for 10 seconds to test agent interaction...")
        await asyncio.sleep(10)
        
        print("\n🔌 Disconnecting...")
        await room.disconnect()
        
        print("✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_client_connection())
