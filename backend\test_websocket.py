#!/usr/bin/env python3
"""
Simple WebSocket client to test the backend WebSocket endpoint
"""

import asyncio
import websockets
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_websocket_connection():
    """Test WebSocket connection to the backend"""
    print("=== WebSocket Connection Test ===")
    
    backend_url = "ws://localhost:8000/ws/audio"
    print(f"Connecting to: {backend_url}")
    
    try:
        # Connect to the backend WebSocket
        async with websockets.connect(backend_url) as websocket:
            print("✓ Connected to backend WebSocket")
            
            # Send a test message
            test_message = {"type": "test", "message": "Hello from test client"}
            await websocket.send(json.dumps(test_message))
            print("✓ Sent test message")
            
            # Wait for a response or timeout
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✓ Received response: {type(response).__name__}")
                if isinstance(response, str):
                    print(f"   Content: {response[:100]}...")
                else:
                    print(f"   Size: {len(response)} bytes")
            except asyncio.TimeoutError:
                print("⚠ No response received within 10 seconds")
            
            # Send end call message
            end_message = {"type": "end_call"}
            await websocket.send(json.dumps(end_message))
            print("✓ Sent end call message")
            
            print("✓ Test completed successfully")
            return True
            
    except ConnectionRefusedError:
        print("✗ Connection refused - is the backend running?")
        print("   Start the backend with: python app.py")
        return False
    except Exception as e:
        print(f"✗ Connection failed: {str(e)}")
        return False

async def test_health_endpoint():
    """Test the health endpoint using HTTP"""
    print("\n=== Health Endpoint Test ===")
    
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print("✓ Health endpoint accessible")
                    print(f"   Status: {data.get('status')}")
                    print(f"   Google API: {'✓' if data.get('google_api_configured') else '✗'}")
                    print(f"   ElevenLabs API: {'✓' if data.get('elevenlabs_api_configured') else '✗'}")
                    print(f"   Voice ID: {'✓' if data.get('voice_id_configured') else '✗'}")
                    return data.get('status') == 'healthy'
                else:
                    print(f"✗ Health endpoint returned status {response.status}")
                    return False
                    
    except ImportError:
        print("⚠ aiohttp not installed, skipping health endpoint test")
        print("   Install with: pip install aiohttp")
        return None
    except Exception as e:
        print(f"✗ Health endpoint test failed: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("Backend WebSocket Test")
    print("=" * 30)
    
    # Test health endpoint first
    health_ok = await test_health_endpoint()
    
    if health_ok is False:
        print("\n❌ Health endpoint indicates configuration issues")
        print("   Please check your .env file and API keys")
        return
    elif health_ok is None:
        print("\n⚠ Could not test health endpoint, proceeding with WebSocket test")
    
    # Test WebSocket connection
    ws_ok = await test_websocket_connection()
    
    print("\n=== Summary ===")
    if health_ok is not None:
        print(f"Health Endpoint: {'✓' if health_ok else '✗'}")
    print(f"WebSocket: {'✓' if ws_ok else '✗'}")
    
    if ws_ok and (health_ok is None or health_ok):
        print("\n🎉 Backend is working correctly!")
        print("   You can now test with the Flutter app")
    else:
        print("\n❌ Backend has issues")
        if not ws_ok:
            print("   - Check if backend is running: python app.py")
            print("   - Check if port 8000 is available")
        if health_ok is False:
            print("   - Check API keys in .env file")

if __name__ == "__main__":
    asyncio.run(main())
