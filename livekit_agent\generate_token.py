#!/usr/bin/env python3
"""
Generate LiveKit access token for testing
"""

import os
from livekit import api
from dotenv import load_dotenv

def generate_token():
    """Generate a LiveKit access token"""
    
    load_dotenv()
    
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    
    if not api_key or not api_secret:
        print("❌ Missing LiveKit credentials in .env file")
        return None
    
    # Create token
    token = api.AccessToken(api_key, api_secret) \
        .with_identity("test-user") \
        .with_name("Test User") \
        .with_grants(api.VideoGrants(
            room_join=True,
            room="test-room",
            can_publish=True,
            can_subscribe=True,
            can_publish_data=True,
        ))
    
    jwt_token = token.to_jwt()
    
    print("✅ Generated LiveKit access token:")
    print(f"Token: {jwt_token}")
    print(f"\nRoom: test-room")
    print(f"Identity: test-user")
    print(f"\nCopy this token to your Flutter app!")
    
    return jwt_token

if __name__ == "__main__":
    generate_token()
