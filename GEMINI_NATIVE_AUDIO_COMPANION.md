# Gemini 2.5 Flash Native Audio Companion App

## Overview
This document outlines the architecture and implementation plan for a Flutter companion app that leverages **modular speech-to-speech (S2S) AI** for seamless audio conversations. The backend is designed to support multiple S2S providers (starting with ElevenLabs S2S), with easy swapping for other models (e.g., Gemini Native Audio) in the future. **LangChain** is used for memory, RAG, and dialog management.

---

## Features
- **Speech-to-Speech AI Dialog:** Users speak to the app and receive natural, expressive audio responses.
- **Modular S2S Backend:** Easily swap between ElevenLabs S2S, Gemini Native Audio, or other providers.
- **LangChain Memory & RAG:** Conversation history, context, and retrieval-augmented generation.
- **Dialog Management:** Handled via LangChain, enabling advanced context and persona.
- **Multilingual & Expressive:** S2S models support multiple languages and expressive voices.

---

## Architecture

### 1. **Frontend (Flutter App)**
- Record user audio (microphone input).
- Send audio to backend via HTTP POST.
- Receive and play back audio responses.
- Display conversation history (optional).

### 2. **Backend (Python, FastAPI/Flask)**
- Receives audio from Flutter app.
- Uses <PERSON>hain for memory, RAG, and dialog context.
- Passes audio and context to the selected S2S model (e.g., ElevenLabs S2S).
- Returns audio response to Flutter app.
- Modular design: swap S2S providers with minimal code changes.

---

## Modular Speech-to-Speech (S2S) Backend

### **S2S Interface**
- Define a base interface for S2S models:
  - `process_audio(audio_bytes, context=None) -> audio_bytes`
- Implementations:
  - **ElevenLabsS2S**: Uses ElevenLabs S2S API for speech-to-speech.
  - **GeminiS2S**: (Future) Uses Gemini Native Audio for speech-to-speech.
  - **OtherS2S**: (Future) Add other providers as needed.

### **Directory Structure Example**
```
backend/
  main.py
  requirements.txt
  s2s/
    base.py         # Abstract S2S interface
    elevenlabs.py   # ElevenLabs S2S implementation
    gemini.py       # Gemini Native Audio implementation (future)
  langchain/
    memory.py
    rag.py
  config.py
```

---

## Implementation Steps

### 1. **Get API Access**
- Sign up for ElevenLabs and obtain API key.
- (Optional) Set up Gemini API for future S2S integration.

### 2. **Flutter App: Audio Handling**
- Record audio using Flutter's audio plugins (e.g., `audio_recorder`, `just_audio`).
- Send recorded audio file (e.g., MP3, WAV) to backend via HTTP POST.
- Receive audio response and play it back using Flutter audio player plugins.

### 3. **Backend: S2S and LangChain Integration**
- Use LangChain for conversation memory and RAG.
- Implement S2S interface and ElevenLabsS2S class.
- Main endpoint: `/dialog/audio` — accepts audio, returns audio.
- Pass context from LangChain to S2S model as needed.

#### **Sample S2S Interface (Python Pseudocode)**
```python
# s2s/base.py
class SpeechToSpeechModel:
    def process_audio(self, audio_bytes, context=None):
        raise NotImplementedError

# s2s/elevenlabs.py
from .base import SpeechToSpeechModel

class ElevenLabsS2S(SpeechToSpeechModel):
    def process_audio(self, audio_bytes, context=None):
        # 1. Send audio to ElevenLabs S2S API
        # 2. Optionally use context from LangChain
        # 3. Return output audio bytes
        pass
```

---

## API Request/Response Structure
- **Input:** Audio file (MP3, WAV, OGG, etc.)
- **Output:** Audio file (for playback)
- **Supported Audio Formats:** Depends on S2S provider (WAV, MP3, etc.)

---

## References
- [ElevenLabs API Docs](https://docs.elevenlabs.io/)
- [LangChain Docs](https://python.langchain.com/)
- [Gemini API Audio Docs](https://ai.google.dev/gemini-api/docs/audio)

---

## Next Steps
1. Set up Python backend with modular S2S interface.
2. Implement ElevenLabs S2S integration.
3. Integrate LangChain for memory/context.
4. Scaffold Flutter app for audio recording and playback.
5. Test end-to-end audio dialog.
6. (Optional) Add other S2S providers (e.g., Gemini Native Audio) as needed.

---

**For further details or code scaffolding, see the official ElevenLabs, LangChain, or Gemini API documentation, or request a sample implementation.** 