  SuppressLint android.annotation  AudioFormat 
android.media  
AudioTrack 
android.media  CHANNEL_INVALID android.media.AudioFormat  CHANNEL_OUT_MONO android.media.AudioFormat  CHANNEL_OUT_STEREO android.media.AudioFormat  ENCODING_PCM_16BIT android.media.AudioFormat  ERROR_BAD_VALUE android.media.AudioTrack  getMinBufferSize android.media.AudioTrack  Handler 
android.os  Looper 
android.os  post android.os.Handler  
getMainLooper android.os.Looper  NonNull androidx.annotation  FlutterWebRTCPlugin com.cloudwebrtc.webrtc  
LocalTrack com.cloudwebrtc.webrtc  
getLocalTrack *com.cloudwebrtc.webrtc.FlutterWebRTCPlugin  getRemoteTrack *com.cloudwebrtc.webrtc.FlutterWebRTCPlugin  sharedSingleton *com.cloudwebrtc.webrtc.FlutterWebRTCPlugin  addSink !com.cloudwebrtc.webrtc.LocalTrack  equals !com.cloudwebrtc.webrtc.LocalTrack  id !com.cloudwebrtc.webrtc.LocalTrack  
removeSink !com.cloudwebrtc.webrtc.LocalTrack  LocalAudioTrack com.cloudwebrtc.webrtc.audio  addSink ,com.cloudwebrtc.webrtc.audio.LocalAudioTrack  id ,com.cloudwebrtc.webrtc.audio.LocalAudioTrack  
removeSink ,com.cloudwebrtc.webrtc.audio.LocalAudioTrack  Noise com.paramsen.noise  close com.paramsen.noise.Noise  equals com.paramsen.noise.Noise  fft com.paramsen.noise.Noise  real com.paramsen.noise.Noise  real "com.paramsen.noise.Noise.Companion  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Any io.livekit.plugin  AudioFormat io.livekit.plugin  
AudioTrack io.livekit.plugin  BUFFER_EXTRA_SIZE io.livekit.plugin  Boolean io.livekit.plugin  
ByteBuffer io.livekit.plugin  	ByteOrder io.livekit.plugin  EMPTY_BUFFER io.livekit.plugin  EventChannel io.livekit.plugin  FFTAudioAnalyzer io.livekit.plugin  Float io.livekit.plugin  
FloatArray io.livekit.plugin  FlutterWebRTCPlugin io.livekit.plugin  Handler io.livekit.plugin  Int io.livekit.plugin  LKAudioTrack io.livekit.plugin  LKLocalAudioTrack io.livekit.plugin  LKRemoteAudioTrack io.livekit.plugin  List io.livekit.plugin  
LiveKitPlugin io.livekit.plugin  Long io.livekit.plugin  Looper io.livekit.plugin  	MAX_CONST io.livekit.plugin  	MIN_CONST io.livekit.plugin  
MethodChannel io.livekit.plugin  Noise io.livekit.plugin  SAMPLE_SIZE io.livekit.plugin  
SHORT_SIZE io.livekit.plugin  Short io.livekit.plugin  
ShortArray io.livekit.plugin  String io.livekit.plugin  TimeUnit io.livekit.plugin  
Visualizer io.livekit.plugin  android io.livekit.plugin  calculateAmplitudeBarsFromFFT io.livekit.plugin  centerBands io.livekit.plugin  check io.livekit.plugin  coerceIn io.livekit.plugin  
component1 io.livekit.plugin  
component2 io.livekit.plugin  	divAssign io.livekit.plugin  easeInOutCubic io.livekit.plugin  forEachIndexed io.livekit.plugin  getValue io.livekit.plugin  indices io.livekit.plugin  invoke io.livekit.plugin  lazy io.livekit.plugin  
mapIndexed io.livekit.plugin  max io.livekit.plugin  minusAssign io.livekit.plugin  mutableMapOf io.livekit.plugin  
plusAssign io.livekit.plugin  pow io.livekit.plugin  provideDelegate io.livekit.plugin  	removeAll io.livekit.plugin  round io.livekit.plugin  set io.livekit.plugin  slice io.livekit.plugin  smoothTransition io.livekit.plugin  sqrt io.livekit.plugin  step io.livekit.plugin  toFloatArray io.livekit.plugin  until io.livekit.plugin  Int io.livekit.plugin.AudioFormat  
bitsPerSample io.livekit.plugin.AudioFormat  numberOfChannels io.livekit.plugin.AudioFormat  
sampleRate io.livekit.plugin.AudioFormat  AudioFormat "io.livekit.plugin.FFTAudioAnalyzer  
AudioTrack "io.livekit.plugin.FFTAudioAnalyzer  BUFFER_EXTRA_SIZE "io.livekit.plugin.FFTAudioAnalyzer  Boolean "io.livekit.plugin.FFTAudioAnalyzer  
ByteBuffer "io.livekit.plugin.FFTAudioAnalyzer  	ByteOrder "io.livekit.plugin.FFTAudioAnalyzer  EMPTY_BUFFER "io.livekit.plugin.FFTAudioAnalyzer  
FloatArray "io.livekit.plugin.FFTAudioAnalyzer  Int "io.livekit.plugin.FFTAudioAnalyzer  Long "io.livekit.plugin.FFTAudioAnalyzer  Noise "io.livekit.plugin.FFTAudioAnalyzer  SAMPLE_SIZE "io.livekit.plugin.FFTAudioAnalyzer  
SHORT_SIZE "io.livekit.plugin.FFTAudioAnalyzer  Short "io.livekit.plugin.FFTAudioAnalyzer  
ShortArray "io.livekit.plugin.FFTAudioAnalyzer  TimeUnit "io.livekit.plugin.FFTAudioAnalyzer  android "io.livekit.plugin.FFTAudioAnalyzer  audioTrackBufferSize "io.livekit.plugin.FFTAudioAnalyzer  check "io.livekit.plugin.FFTAudioAnalyzer  coerceIn "io.livekit.plugin.FFTAudioAnalyzer  	configure "io.livekit.plugin.FFTAudioAnalyzer  durationUsToFrames "io.livekit.plugin.FFTAudioAnalyzer  fft "io.livekit.plugin.FFTAudioAnalyzer  	fftBuffer "io.livekit.plugin.FFTAudioAnalyzer  forEachIndexed "io.livekit.plugin.FFTAudioAnalyzer  
getANDROID "io.livekit.plugin.FFTAudioAnalyzer  
getAndroid "io.livekit.plugin.FFTAudioAnalyzer  getAudioTrackChannelConfig "io.livekit.plugin.FFTAudioAnalyzer  getCHECK "io.livekit.plugin.FFTAudioAnalyzer  getCOERCEIn "io.livekit.plugin.FFTAudioAnalyzer  getCheck "io.livekit.plugin.FFTAudioAnalyzer  getCoerceIn "io.livekit.plugin.FFTAudioAnalyzer  getDefaultBufferSizeInBytes "io.livekit.plugin.FFTAudioAnalyzer  getFOREachIndexed "io.livekit.plugin.FFTAudioAnalyzer  getForEachIndexed "io.livekit.plugin.FFTAudioAnalyzer  getMAX "io.livekit.plugin.FFTAudioAnalyzer  getMINUSAssign "io.livekit.plugin.FFTAudioAnalyzer  getMax "io.livekit.plugin.FFTAudioAnalyzer  getMinusAssign "io.livekit.plugin.FFTAudioAnalyzer  
getPLUSAssign "io.livekit.plugin.FFTAudioAnalyzer  getPcmFrameSize "io.livekit.plugin.FFTAudioAnalyzer  
getPlusAssign "io.livekit.plugin.FFTAudioAnalyzer  getUNTIL "io.livekit.plugin.FFTAudioAnalyzer  getUntil "io.livekit.plugin.FFTAudioAnalyzer  inputAudioFormat "io.livekit.plugin.FFTAudioAnalyzer  isActive "io.livekit.plugin.FFTAudioAnalyzer  max "io.livekit.plugin.FFTAudioAnalyzer  minusAssign "io.livekit.plugin.FFTAudioAnalyzer  noise "io.livekit.plugin.FFTAudioAnalyzer  
plusAssign "io.livekit.plugin.FFTAudioAnalyzer  
processFFT "io.livekit.plugin.FFTAudioAnalyzer  
queueInput "io.livekit.plugin.FFTAudioAnalyzer  release "io.livekit.plugin.FFTAudioAnalyzer  src "io.livekit.plugin.FFTAudioAnalyzer  	srcBuffer "io.livekit.plugin.FFTAudioAnalyzer  srcBufferPosition "io.livekit.plugin.FFTAudioAnalyzer  tempShortArray "io.livekit.plugin.FFTAudioAnalyzer  until "io.livekit.plugin.FFTAudioAnalyzer  AudioFormat ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
AudioTrack ,io.livekit.plugin.FFTAudioAnalyzer.Companion  BUFFER_EXTRA_SIZE ,io.livekit.plugin.FFTAudioAnalyzer.Companion  Boolean ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
ByteBuffer ,io.livekit.plugin.FFTAudioAnalyzer.Companion  	ByteOrder ,io.livekit.plugin.FFTAudioAnalyzer.Companion  EMPTY_BUFFER ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
FloatArray ,io.livekit.plugin.FFTAudioAnalyzer.Companion  Int ,io.livekit.plugin.FFTAudioAnalyzer.Companion  Long ,io.livekit.plugin.FFTAudioAnalyzer.Companion  Noise ,io.livekit.plugin.FFTAudioAnalyzer.Companion  SAMPLE_SIZE ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
SHORT_SIZE ,io.livekit.plugin.FFTAudioAnalyzer.Companion  Short ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
ShortArray ,io.livekit.plugin.FFTAudioAnalyzer.Companion  TimeUnit ,io.livekit.plugin.FFTAudioAnalyzer.Companion  android ,io.livekit.plugin.FFTAudioAnalyzer.Companion  check ,io.livekit.plugin.FFTAudioAnalyzer.Companion  coerceIn ,io.livekit.plugin.FFTAudioAnalyzer.Companion  forEachIndexed ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
getANDROID ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
getAndroid ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getCHECK ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getCOERCEIn ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getCheck ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getCoerceIn ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getFOREachIndexed ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getForEachIndexed ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getMAX ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getMINUSAssign ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getMax ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getMinusAssign ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
getPLUSAssign ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
getPlusAssign ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getUNTIL ,io.livekit.plugin.FFTAudioAnalyzer.Companion  getUntil ,io.livekit.plugin.FFTAudioAnalyzer.Companion  invoke ,io.livekit.plugin.FFTAudioAnalyzer.Companion  max ,io.livekit.plugin.FFTAudioAnalyzer.Companion  minusAssign ,io.livekit.plugin.FFTAudioAnalyzer.Companion  
plusAssign ,io.livekit.plugin.FFTAudioAnalyzer.Companion  until ,io.livekit.plugin.FFTAudioAnalyzer.Companion  AudioTrackSink io.livekit.plugin.LKAudioTrack  String io.livekit.plugin.LKAudioTrack  addSink io.livekit.plugin.LKAudioTrack  equals io.livekit.plugin.LKAudioTrack  id io.livekit.plugin.LKAudioTrack  
removeSink io.livekit.plugin.LKAudioTrack  AudioTrackSink #io.livekit.plugin.LKLocalAudioTrack  LocalAudioTrack #io.livekit.plugin.LKLocalAudioTrack  String #io.livekit.plugin.LKLocalAudioTrack  localAudioTrack #io.livekit.plugin.LKLocalAudioTrack  
AudioTrack $io.livekit.plugin.LKRemoteAudioTrack  AudioTrackSink $io.livekit.plugin.LKRemoteAudioTrack  String $io.livekit.plugin.LKRemoteAudioTrack  
audioTrack $io.livekit.plugin.LKRemoteAudioTrack  
AudioTrack io.livekit.plugin.LiveKitPlugin  BinaryMessenger io.livekit.plugin.LiveKitPlugin  Boolean io.livekit.plugin.LiveKitPlugin  
FlutterPlugin io.livekit.plugin.LiveKitPlugin  FlutterWebRTCPlugin io.livekit.plugin.LiveKitPlugin  Int io.livekit.plugin.LiveKitPlugin  LKAudioTrack io.livekit.plugin.LiveKitPlugin  LKLocalAudioTrack io.livekit.plugin.LiveKitPlugin  LKRemoteAudioTrack io.livekit.plugin.LiveKitPlugin  LocalAudioTrack io.livekit.plugin.LiveKitPlugin  
MethodCall io.livekit.plugin.LiveKitPlugin  
MethodChannel io.livekit.plugin.LiveKitPlugin  NonNull io.livekit.plugin.LiveKitPlugin  Result io.livekit.plugin.LiveKitPlugin  String io.livekit.plugin.LiveKitPlugin  SuppressLint io.livekit.plugin.LiveKitPlugin  
Visualizer io.livekit.plugin.LiveKitPlugin  binaryMessenger io.livekit.plugin.LiveKitPlugin  channel io.livekit.plugin.LiveKitPlugin  
component1 io.livekit.plugin.LiveKitPlugin  
component2 io.livekit.plugin.LiveKitPlugin  flutterWebRTCPlugin io.livekit.plugin.LiveKitPlugin  
getComponent1 io.livekit.plugin.LiveKitPlugin  
getComponent2 io.livekit.plugin.LiveKitPlugin  getMUTABLEMapOf io.livekit.plugin.LiveKitPlugin  getMutableMapOf io.livekit.plugin.LiveKitPlugin  getREMOVEAll io.livekit.plugin.LiveKitPlugin  getRemoveAll io.livekit.plugin.LiveKitPlugin  getSET io.livekit.plugin.LiveKitPlugin  getSet io.livekit.plugin.LiveKitPlugin  handleStartVisualizer io.livekit.plugin.LiveKitPlugin  handleStopVisualizer io.livekit.plugin.LiveKitPlugin  mutableMapOf io.livekit.plugin.LiveKitPlugin  
processors io.livekit.plugin.LiveKitPlugin  	removeAll io.livekit.plugin.LiveKitPlugin  set io.livekit.plugin.LiveKitPlugin  Any io.livekit.plugin.Visualizer  AudioFormat io.livekit.plugin.Visualizer  BinaryMessenger io.livekit.plugin.Visualizer  Boolean io.livekit.plugin.Visualizer  
ByteBuffer io.livekit.plugin.Visualizer  EventChannel io.livekit.plugin.Visualizer  FFTAudioAnalyzer io.livekit.plugin.Visualizer  
FloatArray io.livekit.plugin.Visualizer  Handler io.livekit.plugin.Visualizer  Int io.livekit.plugin.Visualizer  LKAudioTrack io.livekit.plugin.Visualizer  Long io.livekit.plugin.Visualizer  Looper io.livekit.plugin.Visualizer  String io.livekit.plugin.Visualizer  
amplitudes io.livekit.plugin.Visualizer  audioFormat io.livekit.plugin.Visualizer  
audioTrack io.livekit.plugin.Visualizer  bands io.livekit.plugin.Visualizer  barCount io.livekit.plugin.Visualizer  calculateAmplitudeBarsFromFFT io.livekit.plugin.Visualizer  centerBands io.livekit.plugin.Visualizer  eventChannel io.livekit.plugin.Visualizer  	eventSink io.livekit.plugin.Visualizer  ffiAudioAnalyzer io.livekit.plugin.Visualizer   getCALCULATEAmplitudeBarsFromFFT io.livekit.plugin.Visualizer  getCENTERBands io.livekit.plugin.Visualizer   getCalculateAmplitudeBarsFromFFT io.livekit.plugin.Visualizer  getCenterBands io.livekit.plugin.Visualizer  getGETValue io.livekit.plugin.Visualizer  getGetValue io.livekit.plugin.Visualizer  getLAZY io.livekit.plugin.Visualizer  getLazy io.livekit.plugin.Visualizer  
getMAPIndexed io.livekit.plugin.Visualizer  
getMapIndexed io.livekit.plugin.Visualizer  getPROVIDEDelegate io.livekit.plugin.Visualizer  getProvideDelegate io.livekit.plugin.Visualizer  getSLICE io.livekit.plugin.Visualizer  getSMOOTHTransition io.livekit.plugin.Visualizer  getSlice io.livekit.plugin.Visualizer  getSmoothTransition io.livekit.plugin.Visualizer  getTOFloatArray io.livekit.plugin.Visualizer  getToFloatArray io.livekit.plugin.Visualizer  getUNTIL io.livekit.plugin.Visualizer  getUntil io.livekit.plugin.Visualizer  getValue io.livekit.plugin.Visualizer  handler io.livekit.plugin.Visualizer  hiPass io.livekit.plugin.Visualizer  invoke io.livekit.plugin.Visualizer  
isCentered io.livekit.plugin.Visualizer  lazy io.livekit.plugin.Visualizer  loPass io.livekit.plugin.Visualizer  
mapIndexed io.livekit.plugin.Visualizer  provideDelegate io.livekit.plugin.Visualizer  slice io.livekit.plugin.Visualizer  smoothTransition io.livekit.plugin.Visualizer  toFloatArray io.livekit.plugin.Visualizer  until io.livekit.plugin.Visualizer  AudioFormat 	java.lang  
AudioTrack 	java.lang  BUFFER_EXTRA_SIZE 	java.lang  
ByteBuffer 	java.lang  	ByteOrder 	java.lang  EMPTY_BUFFER 	java.lang  EventChannel 	java.lang  FFTAudioAnalyzer 	java.lang  
FloatArray 	java.lang  FlutterWebRTCPlugin 	java.lang  Handler 	java.lang  LKLocalAudioTrack 	java.lang  LKRemoteAudioTrack 	java.lang  Looper 	java.lang  
MethodChannel 	java.lang  Noise 	java.lang  SAMPLE_SIZE 	java.lang  
SHORT_SIZE 	java.lang  Short 	java.lang  
ShortArray 	java.lang  TimeUnit 	java.lang  
Visualizer 	java.lang  android 	java.lang  calculateAmplitudeBarsFromFFT 	java.lang  centerBands 	java.lang  check 	java.lang  coerceIn 	java.lang  
component1 	java.lang  
component2 	java.lang  	divAssign 	java.lang  forEachIndexed 	java.lang  getValue 	java.lang  indices 	java.lang  invoke 	java.lang  lazy 	java.lang  
mapIndexed 	java.lang  max 	java.lang  minusAssign 	java.lang  mutableMapOf 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  	removeAll 	java.lang  set 	java.lang  slice 	java.lang  smoothTransition 	java.lang  step 	java.lang  toFloatArray 	java.lang  until 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  Buffer java.nio  
ByteBuffer java.nio  	ByteOrder java.nio  ShortBuffer java.nio  array java.nio.Buffer  
asShortBuffer java.nio.Buffer  capacity java.nio.Buffer  clear java.nio.Buffer  compact java.nio.Buffer  get java.nio.Buffer  getShort java.nio.Buffer  limit java.nio.Buffer  order java.nio.Buffer  position java.nio.Buffer  put java.nio.Buffer  putShort java.nio.Buffer  allocate java.nio.ByteBuffer  allocateDirect java.nio.ByteBuffer  array java.nio.ByteBuffer  
asShortBuffer java.nio.ByteBuffer  capacity java.nio.ByteBuffer  clear java.nio.ByteBuffer  compact java.nio.ByteBuffer  getShort java.nio.ByteBuffer  limit java.nio.ByteBuffer  order java.nio.ByteBuffer  position java.nio.ByteBuffer  put java.nio.ByteBuffer  putShort java.nio.ByteBuffer  nativeOrder java.nio.ByteOrder  get java.nio.ShortBuffer  TimeUnit java.util.concurrent  MICROSECONDS java.util.concurrent.TimeUnit  SECONDS java.util.concurrent.TimeUnit  convert java.util.concurrent.TimeUnit  Any kotlin  AudioFormat kotlin  
AudioTrack kotlin  BUFFER_EXTRA_SIZE kotlin  Boolean kotlin  	ByteArray kotlin  
ByteBuffer kotlin  	ByteOrder kotlin  Double kotlin  EMPTY_BUFFER kotlin  EventChannel kotlin  FFTAudioAnalyzer kotlin  Float kotlin  
FloatArray kotlin  FlutterWebRTCPlugin kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Handler kotlin  Int kotlin  LKLocalAudioTrack kotlin  LKRemoteAudioTrack kotlin  Lazy kotlin  Long kotlin  Looper kotlin  
MethodChannel kotlin  Noise kotlin  Nothing kotlin  SAMPLE_SIZE kotlin  
SHORT_SIZE kotlin  Short kotlin  
ShortArray kotlin  String kotlin  TimeUnit kotlin  Unit kotlin  
Visualizer kotlin  android kotlin  calculateAmplitudeBarsFromFFT kotlin  centerBands kotlin  check kotlin  coerceIn kotlin  
component1 kotlin  
component2 kotlin  	divAssign kotlin  forEachIndexed kotlin  getValue kotlin  indices kotlin  invoke kotlin  lazy kotlin  
mapIndexed kotlin  max kotlin  minusAssign kotlin  mutableMapOf kotlin  
plusAssign kotlin  provideDelegate kotlin  	removeAll kotlin  set kotlin  slice kotlin  smoothTransition kotlin  step kotlin  toFloatArray kotlin  until kotlin  getPOW 
kotlin.Double  getPow 
kotlin.Double  getCOERCEIn kotlin.Float  getCoerceIn kotlin.Float  getDIVAssign kotlin.Float  getDivAssign kotlin.Float  getMINUSAssign kotlin.Float  getMinusAssign kotlin.Float  
getPLUSAssign kotlin.Float  getPOW kotlin.Float  
getPlusAssign kotlin.Float  getPow kotlin.Float  
getINDICES kotlin.FloatArray  
getIndices kotlin.FloatArray  
getMAPIndexed kotlin.FloatArray  
getMapIndexed kotlin.FloatArray  getSLICE kotlin.FloatArray  getSlice kotlin.FloatArray  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  getMINUSAssign 
kotlin.Int  getMinusAssign 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getFOREachIndexed kotlin.ShortArray  getForEachIndexed kotlin.ShortArray  AudioFormat kotlin.annotation  
AudioTrack kotlin.annotation  BUFFER_EXTRA_SIZE kotlin.annotation  
ByteBuffer kotlin.annotation  	ByteOrder kotlin.annotation  EMPTY_BUFFER kotlin.annotation  EventChannel kotlin.annotation  FFTAudioAnalyzer kotlin.annotation  
FloatArray kotlin.annotation  FlutterWebRTCPlugin kotlin.annotation  Handler kotlin.annotation  LKLocalAudioTrack kotlin.annotation  LKRemoteAudioTrack kotlin.annotation  Looper kotlin.annotation  
MethodChannel kotlin.annotation  Noise kotlin.annotation  SAMPLE_SIZE kotlin.annotation  
SHORT_SIZE kotlin.annotation  Short kotlin.annotation  
ShortArray kotlin.annotation  TimeUnit kotlin.annotation  
Visualizer kotlin.annotation  android kotlin.annotation  calculateAmplitudeBarsFromFFT kotlin.annotation  centerBands kotlin.annotation  check kotlin.annotation  coerceIn kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  	divAssign kotlin.annotation  forEachIndexed kotlin.annotation  getValue kotlin.annotation  indices kotlin.annotation  invoke kotlin.annotation  lazy kotlin.annotation  
mapIndexed kotlin.annotation  max kotlin.annotation  minusAssign kotlin.annotation  mutableMapOf kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  	removeAll kotlin.annotation  set kotlin.annotation  slice kotlin.annotation  smoothTransition kotlin.annotation  step kotlin.annotation  toFloatArray kotlin.annotation  until kotlin.annotation  AudioFormat kotlin.collections  
AudioTrack kotlin.collections  BUFFER_EXTRA_SIZE kotlin.collections  
ByteBuffer kotlin.collections  	ByteOrder kotlin.collections  EMPTY_BUFFER kotlin.collections  EventChannel kotlin.collections  FFTAudioAnalyzer kotlin.collections  
FloatArray kotlin.collections  FlutterWebRTCPlugin kotlin.collections  Handler kotlin.collections  LKLocalAudioTrack kotlin.collections  LKRemoteAudioTrack kotlin.collections  List kotlin.collections  Looper kotlin.collections  
MethodChannel kotlin.collections  
MutableMap kotlin.collections  Noise kotlin.collections  SAMPLE_SIZE kotlin.collections  
SHORT_SIZE kotlin.collections  Short kotlin.collections  
ShortArray kotlin.collections  TimeUnit kotlin.collections  
Visualizer kotlin.collections  android kotlin.collections  calculateAmplitudeBarsFromFFT kotlin.collections  centerBands kotlin.collections  check kotlin.collections  coerceIn kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  	divAssign kotlin.collections  forEachIndexed kotlin.collections  getValue kotlin.collections  indices kotlin.collections  invoke kotlin.collections  lazy kotlin.collections  
mapIndexed kotlin.collections  max kotlin.collections  minusAssign kotlin.collections  mutableMapOf kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  	removeAll kotlin.collections  set kotlin.collections  slice kotlin.collections  smoothTransition kotlin.collections  step kotlin.collections  toFloatArray kotlin.collections  until kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getTOFloatArray kotlin.collections.List  getToFloatArray kotlin.collections.List  MutableEntry kotlin.collections.MutableMap  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  
getComponent1 *kotlin.collections.MutableMap.MutableEntry  
getComponent2 *kotlin.collections.MutableMap.MutableEntry  getREMOVEAll kotlin.collections.MutableSet  getRemoveAll kotlin.collections.MutableSet  AudioFormat kotlin.comparisons  
AudioTrack kotlin.comparisons  BUFFER_EXTRA_SIZE kotlin.comparisons  
ByteBuffer kotlin.comparisons  	ByteOrder kotlin.comparisons  EMPTY_BUFFER kotlin.comparisons  EventChannel kotlin.comparisons  FFTAudioAnalyzer kotlin.comparisons  
FloatArray kotlin.comparisons  FlutterWebRTCPlugin kotlin.comparisons  Handler kotlin.comparisons  LKLocalAudioTrack kotlin.comparisons  LKRemoteAudioTrack kotlin.comparisons  Looper kotlin.comparisons  
MethodChannel kotlin.comparisons  Noise kotlin.comparisons  SAMPLE_SIZE kotlin.comparisons  
SHORT_SIZE kotlin.comparisons  Short kotlin.comparisons  
ShortArray kotlin.comparisons  TimeUnit kotlin.comparisons  
Visualizer kotlin.comparisons  android kotlin.comparisons  calculateAmplitudeBarsFromFFT kotlin.comparisons  centerBands kotlin.comparisons  check kotlin.comparisons  coerceIn kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  	divAssign kotlin.comparisons  forEachIndexed kotlin.comparisons  getValue kotlin.comparisons  indices kotlin.comparisons  invoke kotlin.comparisons  lazy kotlin.comparisons  
mapIndexed kotlin.comparisons  max kotlin.comparisons  minusAssign kotlin.comparisons  mutableMapOf kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  	removeAll kotlin.comparisons  set kotlin.comparisons  slice kotlin.comparisons  smoothTransition kotlin.comparisons  step kotlin.comparisons  toFloatArray kotlin.comparisons  until kotlin.comparisons  AudioFormat 	kotlin.io  
AudioTrack 	kotlin.io  BUFFER_EXTRA_SIZE 	kotlin.io  
ByteBuffer 	kotlin.io  	ByteOrder 	kotlin.io  EMPTY_BUFFER 	kotlin.io  EventChannel 	kotlin.io  FFTAudioAnalyzer 	kotlin.io  
FloatArray 	kotlin.io  FlutterWebRTCPlugin 	kotlin.io  Handler 	kotlin.io  LKLocalAudioTrack 	kotlin.io  LKRemoteAudioTrack 	kotlin.io  Looper 	kotlin.io  
MethodChannel 	kotlin.io  Noise 	kotlin.io  SAMPLE_SIZE 	kotlin.io  
SHORT_SIZE 	kotlin.io  Short 	kotlin.io  
ShortArray 	kotlin.io  TimeUnit 	kotlin.io  
Visualizer 	kotlin.io  android 	kotlin.io  calculateAmplitudeBarsFromFFT 	kotlin.io  centerBands 	kotlin.io  check 	kotlin.io  coerceIn 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  	divAssign 	kotlin.io  forEachIndexed 	kotlin.io  getValue 	kotlin.io  indices 	kotlin.io  invoke 	kotlin.io  lazy 	kotlin.io  
mapIndexed 	kotlin.io  max 	kotlin.io  minusAssign 	kotlin.io  mutableMapOf 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  	removeAll 	kotlin.io  set 	kotlin.io  slice 	kotlin.io  smoothTransition 	kotlin.io  step 	kotlin.io  toFloatArray 	kotlin.io  until 	kotlin.io  AudioFormat 
kotlin.jvm  
AudioTrack 
kotlin.jvm  BUFFER_EXTRA_SIZE 
kotlin.jvm  
ByteBuffer 
kotlin.jvm  	ByteOrder 
kotlin.jvm  EMPTY_BUFFER 
kotlin.jvm  EventChannel 
kotlin.jvm  FFTAudioAnalyzer 
kotlin.jvm  
FloatArray 
kotlin.jvm  FlutterWebRTCPlugin 
kotlin.jvm  Handler 
kotlin.jvm  LKLocalAudioTrack 
kotlin.jvm  LKRemoteAudioTrack 
kotlin.jvm  Looper 
kotlin.jvm  
MethodChannel 
kotlin.jvm  Noise 
kotlin.jvm  SAMPLE_SIZE 
kotlin.jvm  
SHORT_SIZE 
kotlin.jvm  Short 
kotlin.jvm  
ShortArray 
kotlin.jvm  TimeUnit 
kotlin.jvm  
Visualizer 
kotlin.jvm  android 
kotlin.jvm  calculateAmplitudeBarsFromFFT 
kotlin.jvm  centerBands 
kotlin.jvm  check 
kotlin.jvm  coerceIn 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  	divAssign 
kotlin.jvm  forEachIndexed 
kotlin.jvm  getValue 
kotlin.jvm  indices 
kotlin.jvm  invoke 
kotlin.jvm  lazy 
kotlin.jvm  
mapIndexed 
kotlin.jvm  max 
kotlin.jvm  minusAssign 
kotlin.jvm  mutableMapOf 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  	removeAll 
kotlin.jvm  set 
kotlin.jvm  slice 
kotlin.jvm  smoothTransition 
kotlin.jvm  step 
kotlin.jvm  toFloatArray 
kotlin.jvm  until 
kotlin.jvm  AudioFormat kotlin.math  EventChannel kotlin.math  FFTAudioAnalyzer kotlin.math  
FloatArray kotlin.math  Handler kotlin.math  Looper kotlin.math  calculateAmplitudeBarsFromFFT kotlin.math  centerBands kotlin.math  coerceIn kotlin.math  	divAssign kotlin.math  getValue kotlin.math  indices kotlin.math  invoke kotlin.math  lazy kotlin.math  
mapIndexed kotlin.math  max kotlin.math  minusAssign kotlin.math  
plusAssign kotlin.math  pow kotlin.math  provideDelegate kotlin.math  round kotlin.math  slice kotlin.math  smoothTransition kotlin.math  sqrt kotlin.math  step kotlin.math  toFloatArray kotlin.math  until kotlin.math  AudioFormat 
kotlin.ranges  
AudioTrack 
kotlin.ranges  BUFFER_EXTRA_SIZE 
kotlin.ranges  
ByteBuffer 
kotlin.ranges  	ByteOrder 
kotlin.ranges  EMPTY_BUFFER 
kotlin.ranges  EventChannel 
kotlin.ranges  FFTAudioAnalyzer 
kotlin.ranges  
FloatArray 
kotlin.ranges  FlutterWebRTCPlugin 
kotlin.ranges  Handler 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LKLocalAudioTrack 
kotlin.ranges  LKRemoteAudioTrack 
kotlin.ranges  Looper 
kotlin.ranges  
MethodChannel 
kotlin.ranges  Noise 
kotlin.ranges  SAMPLE_SIZE 
kotlin.ranges  
SHORT_SIZE 
kotlin.ranges  Short 
kotlin.ranges  
ShortArray 
kotlin.ranges  TimeUnit 
kotlin.ranges  
Visualizer 
kotlin.ranges  android 
kotlin.ranges  calculateAmplitudeBarsFromFFT 
kotlin.ranges  centerBands 
kotlin.ranges  check 
kotlin.ranges  coerceIn 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  	divAssign 
kotlin.ranges  forEachIndexed 
kotlin.ranges  getValue 
kotlin.ranges  indices 
kotlin.ranges  invoke 
kotlin.ranges  lazy 
kotlin.ranges  
mapIndexed 
kotlin.ranges  max 
kotlin.ranges  minusAssign 
kotlin.ranges  mutableMapOf 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  	removeAll 
kotlin.ranges  set 
kotlin.ranges  slice 
kotlin.ranges  smoothTransition 
kotlin.ranges  step 
kotlin.ranges  toFloatArray 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  getSTEP kotlin.ranges.IntRange  getStep kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  step kotlin.ranges.IntRange  AudioFormat kotlin.sequences  
AudioTrack kotlin.sequences  BUFFER_EXTRA_SIZE kotlin.sequences  
ByteBuffer kotlin.sequences  	ByteOrder kotlin.sequences  EMPTY_BUFFER kotlin.sequences  EventChannel kotlin.sequences  FFTAudioAnalyzer kotlin.sequences  
FloatArray kotlin.sequences  FlutterWebRTCPlugin kotlin.sequences  Handler kotlin.sequences  LKLocalAudioTrack kotlin.sequences  LKRemoteAudioTrack kotlin.sequences  Looper kotlin.sequences  
MethodChannel kotlin.sequences  Noise kotlin.sequences  SAMPLE_SIZE kotlin.sequences  
SHORT_SIZE kotlin.sequences  Short kotlin.sequences  
ShortArray kotlin.sequences  TimeUnit kotlin.sequences  
Visualizer kotlin.sequences  android kotlin.sequences  calculateAmplitudeBarsFromFFT kotlin.sequences  centerBands kotlin.sequences  check kotlin.sequences  coerceIn kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  	divAssign kotlin.sequences  forEachIndexed kotlin.sequences  getValue kotlin.sequences  indices kotlin.sequences  invoke kotlin.sequences  lazy kotlin.sequences  
mapIndexed kotlin.sequences  max kotlin.sequences  minusAssign kotlin.sequences  mutableMapOf kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  	removeAll kotlin.sequences  set kotlin.sequences  slice kotlin.sequences  smoothTransition kotlin.sequences  step kotlin.sequences  toFloatArray kotlin.sequences  until kotlin.sequences  AudioFormat kotlin.text  
AudioTrack kotlin.text  BUFFER_EXTRA_SIZE kotlin.text  
ByteBuffer kotlin.text  	ByteOrder kotlin.text  EMPTY_BUFFER kotlin.text  EventChannel kotlin.text  FFTAudioAnalyzer kotlin.text  
FloatArray kotlin.text  FlutterWebRTCPlugin kotlin.text  Handler kotlin.text  LKLocalAudioTrack kotlin.text  LKRemoteAudioTrack kotlin.text  Looper kotlin.text  
MethodChannel kotlin.text  Noise kotlin.text  SAMPLE_SIZE kotlin.text  
SHORT_SIZE kotlin.text  Short kotlin.text  
ShortArray kotlin.text  TimeUnit kotlin.text  
Visualizer kotlin.text  android kotlin.text  calculateAmplitudeBarsFromFFT kotlin.text  centerBands kotlin.text  check kotlin.text  coerceIn kotlin.text  
component1 kotlin.text  
component2 kotlin.text  	divAssign kotlin.text  forEachIndexed kotlin.text  getValue kotlin.text  indices kotlin.text  invoke kotlin.text  lazy kotlin.text  
mapIndexed kotlin.text  max kotlin.text  minusAssign kotlin.text  mutableMapOf kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  	removeAll kotlin.text  set kotlin.text  slice kotlin.text  smoothTransition kotlin.text  step kotlin.text  toFloatArray kotlin.text  until kotlin.text  
AudioTrack 
org.webrtc  AudioTrackSink 
org.webrtc  MediaStreamTrack 
org.webrtc  addSink org.webrtc.AudioTrack  id org.webrtc.AudioTrack  
removeSink org.webrtc.AudioTrack  addSink org.webrtc.MediaStreamTrack  equals org.webrtc.MediaStreamTrack  id org.webrtc.MediaStreamTrack  
removeSink org.webrtc.MediaStreamTrack                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               