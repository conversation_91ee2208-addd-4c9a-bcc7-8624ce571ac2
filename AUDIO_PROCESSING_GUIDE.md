# Audio Processing and Noise Reduction Guide

## Overview
This guide covers the audio processing improvements implemented to enhance speech recognition quality and reduce background noise in the AI Companion app.

## Current Audio Processing Features

### 1. **Flutter Frontend Improvements**

#### Built-in Audio Processing
```dart
const RecordConfig(
  encoder: AudioEncoder.aacLc,
  sampleRate: 16000,      // Optimal for speech recognition
  numChannels: 1,         // Mono for speech
  bitRate: 64000,         // Lower bitrate, sufficient for speech
  autoGain: true,         // Automatic gain control
  echoCancel: true,       // Echo cancellation
  noiseSuppress: true,    // Noise suppression
)
```

#### Client-Side Filtering
- **Chunk Size Filtering**: Only sends audio chunks > 100 bytes
- **Noise Reduction**: Filters out very small chunks that are likely noise
- **Automatic Gain Control**: Normalizes audio levels

### 2. **Backend Audio Processing**

#### Configurable Filtering
```yaml
audio_processing:
  min_chunk_size: 50          # Minimum audio chunk size to process
  substantial_chunk_size: 100  # Size for new recording detection
  enable_filtering: true       # Enable/disable filtering
  filter_silence: true         # Filter silent chunks
```

#### Processing Pipeline
1. **Size Validation**: Filters chunks smaller than `min_chunk_size`
2. **Session Detection**: Identifies new recording sessions
3. **Quality Control**: Removes noise artifacts
4. **ElevenLabs Optimization**: Optimized settings for speech processing

### 3. **ElevenLabs Optimization**

#### Voice Settings
```yaml
voice_settings:
  stability: 0.5           # Voice consistency
  similarity_boost: 0.8    # Voice similarity enhancement
  style: 0.5              # Speaking style control
  use_speaker_boost: true  # Speaker enhancement
```

#### Generation Config
```yaml
generation_config:
  chunk_length_schedule: [50, 120, 200]  # Progressive chunk sizes
  use_cpu: false          # Use GPU acceleration
  temperature: 0.7        # Response creativity
```

## Audio Quality Improvements

### ✅ **Implemented Features**
- **Echo Cancellation**: Removes audio feedback
- **Noise Suppression**: Reduces background noise
- **Automatic Gain Control**: Normalizes volume levels
- **Chunk Filtering**: Removes noise artifacts
- **Session Management**: Clean recording session handling

### 🔄 **Potential Advanced Features**

#### 1. **Voice Activity Detection (VAD)**
```dart
// Future enhancement: Add VAD to detect speech vs silence
bool isVoiceActivity(Uint8List audioData) {
  // Implement energy-based or ML-based VAD
  return calculateAudioEnergy(audioData) > threshold;
}
```

#### 2. **Spectral Noise Reduction**
```python
# Future enhancement: Advanced noise reduction
import librosa
import noisereduce as nr

def reduce_noise(audio_data, sample_rate=16000):
    # Perform spectral noise reduction
    reduced_noise = nr.reduce_noise(y=audio_data, sr=sample_rate)
    return reduced_noise
```

#### 3. **Real-time Audio Enhancement**
- **Adaptive Filtering**: Adjust filtering based on environment
- **Multi-band Processing**: Process different frequency ranges
- **Dynamic Range Compression**: Optimize audio levels

## Configuration Options

### Basic Settings (config.yaml)
```yaml
audio_processing:
  # Filtering
  min_chunk_size: 50
  substantial_chunk_size: 100
  enable_filtering: true
  
  # Quality
  filter_silence: true
  noise_threshold: 0.1
  
  # Performance
  processing_mode: "realtime"  # or "quality"
```

### Advanced Settings
```yaml
audio_processing:
  advanced:
    # Voice Activity Detection
    vad_enabled: true
    vad_threshold: 0.5
    
    # Noise Reduction
    noise_reduction_enabled: true
    noise_reduction_strength: 0.7
    
    # Audio Enhancement
    auto_gain_target: -20  # dB
    compression_ratio: 3.0
```

## Testing Audio Quality

### 1. **Environment Testing**
- **Quiet Environment**: Test baseline performance
- **Noisy Environment**: Test noise suppression
- **Echo-prone Environment**: Test echo cancellation

### 2. **Audio Metrics**
- **Signal-to-Noise Ratio (SNR)**: Measure audio clarity
- **Recognition Accuracy**: Test speech recognition quality
- **Response Time**: Measure processing latency

### 3. **User Experience Testing**
- **Multiple Speakers**: Test voice isolation
- **Background Music**: Test music suppression
- **Phone Calls**: Test real-world scenarios

## Performance Considerations

### Current Performance
- **Latency**: ~100-200ms processing delay
- **CPU Usage**: Low (hardware-accelerated)
- **Memory**: Minimal buffering
- **Battery**: Optimized for mobile devices

### Optimization Tips
1. **Adjust Chunk Sizes**: Balance quality vs latency
2. **Configure Filtering**: Enable/disable based on environment
3. **Monitor Performance**: Watch for processing bottlenecks
4. **Test Different Settings**: Find optimal configuration

## Troubleshooting

### Common Issues
1. **Audio Too Quiet**: Increase `autoGain` or adjust microphone sensitivity
2. **Too Much Filtering**: Decrease `min_chunk_size`
3. **Background Noise**: Enable `noiseSuppress` and increase filtering
4. **Echo Issues**: Ensure `echoCancel` is enabled

### Debug Settings
```yaml
audio_processing:
  debug:
    log_audio_stats: true
    save_audio_samples: false
    verbose_filtering: true
```

## Future Enhancements

### Planned Features
1. **ML-based Noise Reduction**: Use AI models for advanced noise suppression
2. **Speaker Identification**: Identify and focus on primary speaker
3. **Environmental Adaptation**: Automatically adjust settings based on environment
4. **Real-time Audio Visualization**: Show audio levels and quality metrics

### Integration Options
- **WebRTC Audio Processing**: Advanced browser-based processing
- **TensorFlow Lite**: On-device ML audio enhancement
- **Platform-specific APIs**: iOS/Android native audio processing
