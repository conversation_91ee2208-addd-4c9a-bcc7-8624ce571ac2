import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:just_audio/just_audio.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'dart:typed_data';
import 'dart:io';
import 'dart:convert';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AI Companion',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        primarySwatch: Colors.blue,
      ),
      home: const AudioRecorderScreen(),
    );
  }
}

class AudioRecorderScreen extends StatefulWidget {
  const AudioRecorderScreen({super.key});

  @override
  _AudioRecorderScreenState createState() => _AudioRecorderScreenState();
}

class _AudioRecorderScreenState extends State<AudioRecorderScreen> {
  final _audioRecorder = AudioRecorder();
  final _audioPlayer = AudioPlayer(); // Initialized once
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _audioPath;
  WebSocketChannel? _channel;
  String _webSocketStatus = 'Disconnected';

  // Using ConcatenatingAudioSource to handle multiple audio chunks for playback
  final _audioDataQueue = ConcatenatingAudioSource(children: []);


  @override
  void initState() {
    super.initState();
    _requestPermissions().then((_) {
      // Initialize WebSocket after permissions are granted
      _initWebSocket();
    });

    _audioPlayer.setAudioSource(_audioDataQueue); // Set source for the player

    _audioPlayer.playerStateStream.listen((state) {
      if (state.playing) {
        setState(() { _isPlaying = true; });
      } else {
        setState(() { _isPlaying = false; });
      }
      if (state.processingState == ProcessingState.completed) {
        setState(() { _isPlaying = false; });
        _audioDataQueue.clear(); // Clear queue when playback of all items is done
      }
    });
  }

  @override
  void dispose() {
    _audioRecorder.dispose();
    _audioPlayer.dispose();
    _channel?.sink.close();
    super.dispose();
  }

  Future<void> _requestPermissions() async {
    await Permission.microphone.request();
    // Handle other permissions like storage if needed
  }

  // Initialize WebSocket connection
  Future<void> _initWebSocket() async {
    if (_channel != null) return;
    final wsUrl = Uri.parse('ws://192.168.1.2:8000/ws/audio');
    setState(() {
      _webSocketStatus = 'Connecting...';
    });
    try {
      _channel = WebSocketChannel.connect(wsUrl);
      setState(() {
        _webSocketStatus = 'Ready';
      });
      debugPrint('[_initWebSocket] WebSocket connected to $wsUrl');
      _channel!.stream.listen(
        (message) {
          debugPrint('[_initWebSocket] WebSocket message received (${message.runtimeType})');
          if (message is List<int>) {
            _playAudio(Uint8List.fromList(message));
          } else if (message is String) {
            debugPrint('[_initWebSocket] Received text message: $message');
            try {
              var decodedMessage = jsonDecode(message);
              if (decodedMessage is Map) {
                debugPrint('[_initWebSocket] Server message: $decodedMessage');
              }
            } catch (e) {
              debugPrint('[_initWebSocket] Error parsing message: $e');
            }
          }
        },
        onDone: () {
          debugPrint('[_initWebSocket] WebSocket connection closed (onDone)');
          setState(() {
            _webSocketStatus = 'Disconnected - tap to reconnect';
            _isRecording = false;
            _channel = null;
          });
        },
        onError: (error) {
          debugPrint('[_initWebSocket] WebSocket error: $error');
          setState(() {
            _webSocketStatus = 'Connection error - tap to retry';
            _isRecording = false;
            _channel = null;
          });
        },
        cancelOnError: true,
      );
    } catch (e) {
      debugPrint('[_initWebSocket] Failed to connect to WebSocket: $e');
      setState(() {
        _webSocketStatus = 'Connection failed - tap to retry';
        _channel = null;
      });
      rethrow;
    }
  }

  Future<void> _startRecording() async {
    debugPrint('[_startRecording] Called.');
    if (await _audioRecorder.isRecording()) return;
    if (!await Permission.microphone.isGranted) {
      final status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        debugPrint('[_startRecording] Microphone permission not granted');
        setState(() {
          _webSocketStatus = 'Microphone permission required';
        });
        return;
      }
    }
    try {
      // Start recording audio stream
      final stream = await _audioRecorder.startStream(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          sampleRate: 16000,
          numChannels: 1,
          bitRate: 128000,
        ),
      );
      debugPrint('[_startRecording] Using audio format: AAC LC, 16000 Hz, Mono, 128kbps');
      setState(() {
        _isRecording = true;
        _webSocketStatus = 'Listening...';
      });
      // Stream audio to WebSocket
      stream.listen(
        (audioChunkBytes) {
          debugPrint('[_startRecording] Audio chunk received (${audioChunkBytes.length} bytes)');
          if (_channel != null && _isRecording) {
            try {
              _channel!.sink.add(audioChunkBytes);
              debugPrint('[_startRecording] Audio chunk sent to WebSocket');
            } catch (e) {
              debugPrint('[_startRecording] Error sending audio chunk: $e');
            }
          }
        },
        onError: (e) {
          debugPrint('[_startRecording] Error in audio stream: $e');
          setState(() {
            _isRecording = false;
            _webSocketStatus = 'Error - tap to retry';
          });
        },
        onDone: () {
          debugPrint('[_startRecording] Audio streaming finished (onDone)');
          setState(() {
            _isRecording = false;
            _webSocketStatus = 'Tap to speak';
          });
        },
        cancelOnError: true,
      );
      debugPrint('[_startRecording] Audio stream listener attached.');
    } catch (e) {
      debugPrint('[_startRecording] Error starting recording: $e');
      setState(() {
        _isRecording = false;
        _webSocketStatus = 'Error - tap to retry';
      });
    }
    debugPrint('[_startRecording] Exiting.');
  }

  // End the call and close the WebSocket connection
  Future<void> _endCall() async {
    try {
      // First stop the recorder if it's running
      if (await _audioRecorder.isRecording()) {
        await _audioRecorder.stop();
      }

      // Close the WebSocket connection
      if (_channel != null) {
        try {
          debugPrint('Closing WebSocket connection');
          await _channel!.sink.close();
          debugPrint('WebSocket connection closed');
        } catch (e) {
          debugPrint('Error closing WebSocket: $e');
        } finally {
          _channel = null;
        }
      }

      setState(() {
        _isRecording = false;
        _webSocketStatus = 'Call ended';
      });

    } catch (e) {
      debugPrint('Error ending call: $e');
      setState(() {
        _isRecording = false;
        _webSocketStatus = 'Error: ${e.toString()}';
      });
    }
  }

  // Toggle recording state or handle reconnection
  Future<void> _toggleRecording() async {
    debugPrint('[_toggleRecording] Called. isRecording=$_isRecording, _channel=$_channel');
    if (_channel == null || _channel!.closeCode != null) {
      // Attempt to reconnect if not connected
      try {
        debugPrint('[_toggleRecording] Attempting to reconnect WebSocket...');
        await _initWebSocket();
        // Small delay to allow connection to establish
        await Future.delayed(const Duration(milliseconds: 300));
      } catch (e) {
        debugPrint('[_toggleRecording] Failed to reconnect: $e');
        return;
      }
    }
    if (!_isRecording) {
      debugPrint('[_toggleRecording] Starting recording...');
      await _startRecording();
      debugPrint('[_toggleRecording] Recording started.');
    } else {
      debugPrint('[_toggleRecording] Stopping recording...');
      await _audioRecorder.stop(); // Actually stop the recorder

      // Send end of recording message to backend
      if (_channel != null) {
        try {
          final endMessage = jsonEncode({'type': 'end_recording'});
          _channel!.sink.add(endMessage);
          debugPrint('[_toggleRecording] Sent end_recording message');
        } catch (e) {
          debugPrint('[_toggleRecording] Error sending end_recording message: $e');
        }
      }

      setState(() {
        _isRecording = false;
        _webSocketStatus = 'Ready';
      });
    }
    debugPrint('[_toggleRecording] Exiting.');
  }

  Future<void> _playAudio(Uint8List audioBytes) async {
    try {
      // Create a MemoryAudioSource for the new chunk
      final audioSource = MyBytesAudioSource(audioBytes);
      await _audioDataQueue.add(audioSource);

      if (!_audioPlayer.playing && (_audioDataQueue.length > 0 || _audioPlayer.playerState.processingState == ProcessingState.ready)) {
           // Start playing only if not already playing and there's data or player is ready
           _audioPlayer.play();
      }
      // _isPlaying state is managed by the playerStateStream listener
    } catch (e) {
      debugPrint("Error adding audio to queue or playing: $e");
      // _isPlaying state is managed by the playerStateStream listener
    }
  }


  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: const Text('AI Companion'),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Status: $_webSocketStatus'),
            const SizedBox(height: 40),
            // Mic button for toggling recording
            GestureDetector(
              onTap: _toggleRecording,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: _isRecording ? Colors.red.withOpacity(0.2) : Colors.blue.withOpacity(0.2),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _isRecording ? Colors.red : Colors.blue,
                    width: 4,
                  ),
                ),
                child: Icon(
                  _isRecording ? Icons.mic : Icons.mic_none,
                  size: 60,
                  color: _isRecording ? Colors.red : Colors.blue,
                ),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              _isRecording ? 'Listening... Tap to mute' : 'Tap to speak',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 40),
            if (_isPlaying)
              const Column(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 10),
                  Text('AI is speaking...'),
                ],
              ),
          ],
        ),
      ),
    );
  }
}

// Add this class somewhere in your file, or a separate file
class MyBytesAudioSource extends StreamAudioSource {
  final Uint8List _buffer;

  MyBytesAudioSource(this._buffer) : super(tag: 'MyBytesAudioSource'); // Added tag

  @override
  Future<StreamAudioResponse> request([int? start, int? end]) async {
    start ??= 0;
    end ??= _buffer.length;
    return StreamAudioResponse(
      sourceLength: _buffer.length,
      contentLength: end - start,
      offset: start,
      stream: Stream.value(_buffer.sublist(start, end)),
      contentType: 'audio/aac', // Assuming AAC, adjust if backend sends different
    );
  }
}
