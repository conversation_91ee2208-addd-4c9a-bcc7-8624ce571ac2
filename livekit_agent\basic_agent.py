#!/usr/bin/env python3
"""
Basic LiveKit Agent for Testing Connection
Just tests if we can connect to LiveKit and receive audio
"""

import asyncio
import logging
import os

from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
)
from livekit import rtc

# Environment setup
from dotenv import load_dotenv
load_dotenv()

logger = logging.getLogger("basic-agent")
logger.setLevel(logging.INFO)

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent"""
    
    logger.info("🚀 Starting basic LiveKit agent...")
    
    # Wait for the first participant to connect
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    
    logger.info("✅ Connected to LiveKit room!")
    logger.info(f"Room name: {ctx.room.name}")
    logger.info(f"Room SID: {ctx.room.sid}")
    
    # Set up event handlers
    @ctx.room.on("participant_connected")
    def on_participant_connected(participant: rtc.RemoteParticipant):
        logger.info(f"👤 Participant connected: {participant.identity}")
    
    @ctx.room.on("participant_disconnected") 
    def on_participant_disconnected(participant: rtc.RemoteParticipant):
        logger.info(f"👋 Participant disconnected: {participant.identity}")
    
    @ctx.room.on("track_subscribed")
    def on_track_subscribed(track: rtc.Track, publication: rtc.TrackPublication, participant: rtc.RemoteParticipant):
        logger.info(f"🎵 Track subscribed: {track.kind} from {participant.identity}")
        
        if track.kind == rtc.TrackKind.KIND_AUDIO:
            logger.info("🎤 Audio track received - ready for voice processing!")
    
    logger.info("🎯 Agent is ready and waiting for participants...")
    logger.info("💡 Try connecting from your Flutter app now!")
    
    # Keep the agent running
    while True:
        await asyncio.sleep(5)
        logger.info(f"💓 Agent heartbeat - {len(ctx.room.remote_participants)} participants connected")

if __name__ == "__main__":
    # Configure worker options
    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
    )
    
    logger.info("🔧 Starting LiveKit agent worker...")
    
    # Start the agent
    cli.run_app(worker_options)
