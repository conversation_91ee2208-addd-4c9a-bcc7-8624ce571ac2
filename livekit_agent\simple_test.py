#!/usr/bin/env python3
"""
Simple test to verify LiveKit setup
"""

import os
import asyncio
from livekit import api
from dotenv import load_dotenv

async def simple_test():
    """Simple LiveKit test"""
    
    load_dotenv()
    
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    livekit_url = os.getenv("LIVEKIT_URL")
    
    print("🔧 LiveKit Simple Test")
    print("=" * 40)
    print(f"URL: {livekit_url}")
    print(f"API Key: {api_key}")
    print(f"API Secret: {api_secret[:8]}...")
    
    # Generate a token
    print("\n🎫 Generating access token...")
    try:
        token = api.AccessToken(api_key, api_secret) \
            .with_identity("test-user") \
            .with_name("Test User") \
            .with_grants(api.VideoGrants(
                room_join=True,
                room="test-room",
                can_publish=True,
                can_subscribe=True,
                can_publish_data=True,
            ))
        
        jwt_token = token.to_jwt()
        print(f"✅ Token generated successfully!")
        print(f"Token: {jwt_token}")
        
        print(f"\n🎯 Basic LiveKit setup is working!")
        print(f"📱 The agent should be able to connect when a client joins")
        
        return True
        
    except Exception as e:
        print(f"❌ Token generation failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(simple_test())
