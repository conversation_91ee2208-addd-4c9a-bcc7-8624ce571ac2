{"buildFiles": ["C:\\Development\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Development\\sol-ai\\voice_ai_flutter_app\\android\\app\\.cxx\\Debug\\6p6x2w39\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Development\\sol-ai\\voice_ai_flutter_app\\android\\app\\.cxx\\Debug\\6p6x2w39\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}