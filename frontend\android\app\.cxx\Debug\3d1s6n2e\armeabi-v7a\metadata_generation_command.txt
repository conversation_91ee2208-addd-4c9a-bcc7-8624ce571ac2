                        -HC:\Development\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Development\sol-ai\frontend\build\app\intermediates\cxx\Debug\3d1s6n2e\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Development\sol-ai\frontend\build\app\intermediates\cxx\Debug\3d1s6n2e\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Development\sol-ai\frontend\android\app\.cxx\Debug\3d1s6n2e\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2