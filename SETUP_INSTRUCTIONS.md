# Setup Instructions for AI Companion

## Quick Fix for WebSocket Connection Issues

The WebSocket connection is closing immediately due to missing configuration. Follow these steps to fix it:

### 1. Backend Setup

#### Step 1: Create Environment File
```bash
cd backend
cp .env.example .env
```

#### Step 2: Add Your API Keys to `.env`
Edit the `.env` file and add your actual API keys:

```env
# Google Generative AI API Key
# Get this from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_actual_google_api_key_here

# ElevenLabs API Key
# Get this from: https://elevenlabs.io/app/settings/api-keys
ELEVENLABS_API_KEY=your_actual_elevenlabs_api_key_here
```

#### Step 3: Test Configuration
Run the configuration test script:
```bash
cd backend
python test_config.py
```

This will verify:
- ✓ Environment variables are set
- ✓ Configuration file is valid
- ✓ ElevenLabs WebSocket connection works

#### Step 4: Start Backend
```bash
cd backend
python app.py
```

#### Step 5: Test Health Endpoint
Open your browser and go to: `http://localhost:8000/health`

You should see:
```json
{
  "status": "healthy",
  "google_api_configured": true,
  "elevenlabs_api_configured": true,
  "voice_id_configured": true,
  "voice_id": "At0PgxDCGLVlDr0N86Ty"
}
```

### 2. Frontend Setup

#### Update Backend URL
In `frontend/lib/main.dart`, line 104, update the WebSocket URL to match your backend:

```dart
final wsUrl = Uri.parse('ws://localhost:8000/ws/audio');  // For local testing
// OR
final wsUrl = Uri.parse('ws://***********:8000/ws/audio');  // For network testing
```

#### Run Frontend
```bash
cd frontend
flutter run
```

### 3. Common Issues and Solutions

#### Issue: "ELEVENLABS_API_KEY not configured"
- **Solution**: Make sure you created the `.env` file and added your ElevenLabs API key

#### Issue: "Failed to connect to ElevenLabs WebSocket"
- **Solution**:
  1. Verify your ElevenLabs API key is valid
  2. Check your internet connection
  3. Run `python test_config.py` to test the connection

#### Issue: "GOOGLE_API_KEY not found"
- **Solution**: Add your Google Generative AI API key to the `.env` file

#### Issue: Connection closes immediately
- **Solution**: This was the main issue - missing API keys. Follow steps 1-2 above.

### 4. Getting API Keys

#### Google Generative AI API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy it to your `.env` file

#### ElevenLabs API Key
1. Go to [ElevenLabs Settings](https://elevenlabs.io/app/settings/api-keys)
2. Create a new API key
3. Copy it to your `.env` file

### 5. Verification Steps

1. **Backend Health Check**: `curl http://localhost:8000/health`
2. **Configuration Test**: `python backend/test_config.py`
3. **WebSocket Test**: Connect with the Flutter app and check logs

### 6. Testing Tools

#### Backend Configuration Test
```bash
cd backend
python test_config.py
```

#### Backend WebSocket Test
```bash
cd backend
python test_websocket.py
```

### 7. Expected Log Output (Success)

When working correctly, you should see logs like:
```
INFO - WebSocket connection accepted from 127.0.0.1:xxxxx
INFO - Connecting to ElevenLabs WebSocket: wss://api.elevenlabs.io/v1/text-to-speech/At0PgxDCGLVlDr0N86Ty/stream-input?model_id=eleven_monolingual_v1
INFO - Successfully connected to ElevenLabs WebSocket
INFO - Sent initialization message to ElevenLabs WebSocket
```

Instead of the previous error:
```
ERROR - ELEVENLABS_API_KEY not found for WebSocket connection
INFO - WebSocket connection closed
```

### 8. Troubleshooting Steps

1. **Run configuration test**: `python backend/test_config.py`
2. **Check health endpoint**: Visit `http://localhost:8000/health`
3. **Test WebSocket**: `python backend/test_websocket.py`
4. **Check backend logs** for specific error messages
5. **Verify network connectivity** between frontend and backend
