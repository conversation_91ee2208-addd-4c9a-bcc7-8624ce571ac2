import 'package:flutter/material.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'LiveKit Test App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const LiveKitTestPage(),
    );
  }
}

class LiveKitTestPage extends StatefulWidget {
  const LiveKitTestPage({super.key});

  @override
  State<LiveKitTestPage> createState() => _LiveKitTestPageState();
}

class _LiveKitTestPageState extends State<LiveKitTestPage> {
  Room? _room;
  bool _isConnected = false;
  bool _isConnecting = false;
  String _status = 'Disconnected';

  // Your LiveKit configuration
  static const String livekitUrl = 'wss://test-6hztxoof.livekit.cloud';
  static const String livekitToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiVGVzdCBVc2VyIiwidmlkZW8iOnsicm9vbUpvaW4iOnRydWUsInJvb20iOiJ0ZXN0LXJvb20iLCJjYW5QdWJsaXNoIjp0cnVlLCJjYW5TdWJzY3JpYmUiOnRydWUsImNhblB1Ymxpc2hEYXRhIjp0cnVlfSwic3ViIjoidGVzdC11c2VyIiwiaXNzIjoiQVBJZGRMM3RjRnV1TjRLIiwibmJmIjoxNzQ4MTE1OTc5LCJleHAiOjE3NDgxMzc1Nzl9.wcv96vKo1ZEJyC3Nlz5Scd8BmRb8tXB1kMgUT6-lDQU';

  @override
  void initState() {
    super.initState();
    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    await Permission.microphone.request();
    await Permission.camera.request();
  }

  Future<void> _connectToRoom() async {
    if (_isConnecting || _isConnected) return;

    setState(() {
      _isConnecting = true;
      _status = 'Connecting to LiveKit...';
    });

    try {
      // Create room instance
      _room = Room();

      // Set up event listeners
      _room!.addListener(_onRoomUpdate);

      // Connect to room
      await _room!.connect(
        livekitUrl,
        livekitToken,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
        ),
        fastConnectOptions: FastConnectOptions(
          microphone: TrackOption(enabled: true),
          camera: TrackOption(enabled: false),
        ),
      );

      setState(() {
        _isConnected = true;
        _isConnecting = false;
        _status = 'Connected! Agent should respond now.';
      });

      debugPrint('✅ Connected to LiveKit room successfully!');

    } catch (e) {
      setState(() {
        _isConnecting = false;
        _status = 'Connection failed: $e';
      });
      debugPrint('❌ Connection error: $e');
    }
  }

  Future<void> _disconnectFromRoom() async {
    if (_room != null) {
      await _room!.disconnect();
      _room!.removeListener(_onRoomUpdate);
      _room = null;
    }

    setState(() {
      _isConnected = false;
      _status = 'Disconnected';
    });
  }

  void _onRoomUpdate() {
    setState(() {
      if (_room?.connectionState == ConnectionState.connected) {
        _status = 'Connected! Agent should respond now.';
      } else if (_room?.connectionState == ConnectionState.disconnected) {
        _status = 'Disconnected';
        _isConnected = false;
      } else if (_room?.connectionState == ConnectionState.connecting) {
        _status = 'Connecting...';
      }
    });
  }

  @override
  void dispose() {
    _disconnectFromRoom();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('LiveKit Test App'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _isConnected ? Icons.check_circle : Icons.radio_button_unchecked,
                size: 80,
                color: _isConnected ? Colors.green : Colors.grey,
              ),
              const SizedBox(height: 20),

              Text(
                'Status: $_status',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Connection button
              if (!_isConnected && !_isConnecting)
                ElevatedButton.icon(
                  onPressed: _connectToRoom,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Connect to Agent'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  ),
                ),

              // Disconnect button
              if (_isConnected)
                ElevatedButton.icon(
                  onPressed: _disconnectFromRoom,
                  icon: const Icon(Icons.stop),
                  label: const Text('Disconnect'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  ),
                ),

              // Loading indicator
              if (_isConnecting)
                const Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Connecting to LiveKit agent...'),
                  ],
                ),

              const SizedBox(height: 40),

              // Instructions
              Text(
                _isConnected
                    ? '🎉 Success! The LiveKit agent is connected and ready.\n\nCheck the agent logs to see the connection.'
                    : '📱 This is a simple test to verify LiveKit connection.\n\nTap "Connect to Agent" to test.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
