import 'package:flutter/material.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Voice AI Companion',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const VoiceAssistantPage(),
    );
  }
}

class VoiceAssistantPage extends StatefulWidget {
  const VoiceAssistantPage({super.key});

  @override
  State<VoiceAssistantPage> createState() => _VoiceAssistantPageState();
}

class _VoiceAssistantPageState extends State<VoiceAssistantPage> {
  Room? _room;
  bool _isConnected = false;
  bool _isConnecting = false;
  String _status = 'Disconnected';

  // LiveKit configuration
  static const String livekitUrl = 'wss://test-6hztxoof.livekit.cloud';
  static const String livekitToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiVGVzdCBVc2VyIiwidmlkZW8iOnsicm9vbUpvaW4iOnRydWUsInJvb20iOiJ0ZXN0LXJvb20iLCJjYW5QdWJsaXNoIjp0cnVlLCJjYW5TdWJzY3JpYmUiOnRydWUsImNhblB1Ymxpc2hEYXRhIjp0cnVlfSwic3ViIjoidGVzdC11c2VyIiwiaXNzIjoiQVBJZGRMM3RjRnV1TjRLIiwibmJmIjoxNzQ4MTE2Mjc0LCJleHAiOjE3NDgxMzc4NzR9.wdfT0R0fPTEgkm-H51hvQQGWz-bS6MQT_vergSpdfbQ';

  @override
  void initState() {
    super.initState();
    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    await Permission.microphone.request();
    await Permission.camera.request();
  }

  String _getAccessToken() {
    // Using pre-generated token for demo
    // In production, generate this on your backend server
    return livekitToken;
  }

  Future<void> _connectToRoom() async {
    if (_isConnecting || _isConnected) return;

    setState(() {
      _isConnecting = true;
      _status = 'Connecting...';
    });

    try {
      // Get access token
      final token = _getAccessToken();

      // Create room instance
      _room = Room();

      // Set up event listeners
      _room!.addListener(_onRoomUpdate);

      // Connect to room
      await _room!.connect(
        livekitUrl,
        token,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
        ),
        fastConnectOptions: FastConnectOptions(
          microphone: TrackOption(enabled: true),
          camera: TrackOption(enabled: false),
        ),
      );

      setState(() {
        _isConnected = true;
        _isConnecting = false;
        _status = 'Connected - Voice AI is ready! Start speaking.';
      });

    } catch (e) {
      setState(() {
        _isConnecting = false;
        _status = 'Connection failed: $e';
      });
      debugPrint('Connection error: $e');
    }
  }

  Future<void> _disconnectFromRoom() async {
    if (_room != null) {
      await _room!.disconnect();
      _room!.removeListener(_onRoomUpdate);
      _room = null;
    }

    setState(() {
      _isConnected = false;
      _status = 'Disconnected';
    });
  }

  void _onRoomUpdate() {
    setState(() {
      // Update UI based on room state
      if (_room?.connectionState == ConnectionState.connected) {
        _status = 'Connected - Voice AI is ready! Start speaking.';
      } else if (_room?.connectionState == ConnectionState.disconnected) {
        _status = 'Disconnected';
        _isConnected = false;
      } else if (_room?.connectionState == ConnectionState.connecting) {
        _status = 'Connecting...';
      }
    });
  }

  Future<void> _toggleMicrophone() async {
    if (_room == null) return;

    final localParticipant = _room!.localParticipant;
    final micPublication = localParticipant?.audioTrackPublications.where(
      (pub) => pub.source == TrackSource.microphone,
    ).firstOrNull;

    if (micPublication != null) {
      await micPublication.muted ? micPublication.unmute() : micPublication.mute();
      setState(() {});
    }
  }

  bool get _isMicrophoneMuted {
    if (_room == null) return true;

    final localParticipant = _room!.localParticipant;
    final micPublication = localParticipant?.audioTrackPublications.where(
      (pub) => pub.source == TrackSource.microphone,
    ).firstOrNull;

    return micPublication?.muted ?? true;
  }

  @override
  void dispose() {
    _disconnectFromRoom();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('🎤 Voice AI Companion'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Status: $_status',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),

            // Connection button
            if (!_isConnected && !_isConnecting)
              ElevatedButton.icon(
                onPressed: _connectToRoom,
                icon: const Icon(Icons.mic),
                label: const Text('Start Voice Chat'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),

            // Disconnect button
            if (_isConnected)
              ElevatedButton.icon(
                onPressed: _disconnectFromRoom,
                icon: const Icon(Icons.call_end),
                label: const Text('End Voice Chat'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),

            // Loading indicator
            if (_isConnecting)
              const Column(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Connecting to AI Assistant...'),
                ],
              ),

            const SizedBox(height: 40),

            // Microphone control (only when connected)
            if (_isConnected) ...[
              GestureDetector(
                onTap: _toggleMicrophone,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: _isMicrophoneMuted
                        ? Colors.red.withOpacity(0.2)
                        : Colors.blue.withOpacity(0.2),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _isMicrophoneMuted ? Colors.red : Colors.blue,
                      width: 3,
                    ),
                  ),
                  child: Icon(
                    _isMicrophoneMuted ? Icons.mic_off : Icons.mic,
                    size: 50,
                    color: _isMicrophoneMuted ? Colors.red : Colors.blue,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _isMicrophoneMuted ? 'Microphone muted' : 'Microphone active',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],

            const SizedBox(height: 40),

            // Instructions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _isConnected
                    ? '🎙️ Voice AI is listening! Just speak naturally and the AI will respond with voice. No need to press any buttons.'
                    : '🚀 Tap "Start Voice Chat" to begin your conversation with the AI assistant.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
