import asyncio
import websockets
import json

async def test_websocket():
    try:
        print("Connecting to WebSocket...")
        ws = await websockets.connect('ws://localhost:8000/ws/audio')
        print("Connected!")
        
        # Send test message
        test_msg = {'type': 'test', 'message': 'hello'}
        await ws.send(json.dumps(test_msg))
        print("Sent test message")
        
        # Wait for response
        response = await asyncio.wait_for(ws.recv(), timeout=10.0)
        print('Response:', response)
        
        await ws.close()
        print("Connection closed")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_websocket())
    print("Test result:", "SUCCESS" if result else "FAILED")
